# 数据权限方法注解功能说明

## 功能概述

在现有的数据权限过滤基础上，新增了基于方法注解的过滤机制。当 Mapper 方法上标注了 `@NoCreator` 注解时，该方法执行的 SQL 将不会添加 creator 查询条件。

## 核心特性

### 1. 注解驱动
- 使用 `@NoCreator` 注解标注 Mapper 方法
- 支持注解描述信息，便于代码维护
- 支持完全跳过数据权限检查的选项

### 2. 无侵入性
- 不影响现有的数据权限配置
- 与现有的实体类注解 `@CreatorPermission` 兼容
- 方法级别的精确控制

### 3. 高性能
- 使用缓存机制避免重复反射
- ThreadLocal 存储当前执行上下文
- 拦截器自动管理生命周期

## 实现架构

### 核心组件

#### 1. `@NoCreator` 注解
```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface NoCreator {
    String value() default "";        // 描述信息
    boolean skipAll() default false;  // 是否完全跳过数据权限检查
}
```

#### 2. `MethodAnnotationDetector` 工具类
- 检测 Mapper 方法上的 `@NoCreator` 注解
- 缓存检测结果，提高性能
- 支持缓存管理和监控

#### 3. `AnnotationAwareCreatorDataPermissionRule` 数据权限规则
- 继承现有的智能数据权限规则
- 增加方法注解检测逻辑
- 当检测到 `@NoCreator` 注解时跳过 creator 权限控制

#### 4. `MappedStatementInterceptor` MyBatis 拦截器
- 拦截 MyBatis 的查询和更新操作
- 将当前执行的 MappedStatement 存储到 ThreadLocal
- 自动管理 ThreadLocal 生命周期，避免内存泄漏

## 使用方法

### 1. 基本使用
```java
@Mapper
public interface UserMapper extends BaseMapperX<UserDO> {
    
    // 普通查询，会应用 creator 数据权限
    List<UserDO> selectByStatus(Integer status);
    
    // 管理员查询，不应用 creator 数据权限
    @NoCreator("管理员查看所有用户")
    List<UserDO> selectAllForAdmin();
}
```

### 2. 统计查询
```java
@Mapper
public interface ReportMapper extends BaseMapperX<ReportDO> {
    
    // 统计查询通常不需要 creator 权限控制
    @NoCreator("统计查询不需要 creator 权限控制")
    @Select("SELECT COUNT(*) FROM report WHERE deleted = 0")
    Long countAll();
}
```

### 3. 系统级查询
```java
@Mapper
public interface SystemMapper extends BaseMapperX<SystemDO> {
    
    // 系统级查询，跳过所有数据权限检查
    @NoCreator(value = "系统级别查询", skipAll = true)
    List<SystemDO> selectForSystemReport();
}
```

## 配置说明

### 1. 现有配置保持不变
```yaml
wsapp:
  data-permission:
    enable: true
    mode: smart  # 智能检测模式
    scan-packages:
      - "cn.aguyao.module.wsapp.dal.dataobject"
    admin-roles:
      - "1"
      - "admin"
    super-admin-users:
      - 1
      - 100
    exclude_tables:
      # - "wsapp_user_port"
```

### 2. 自动配置
- 系统会自动注册 `MappedStatementInterceptor` 拦截器
- 自动创建 `AnnotationAwareCreatorDataPermissionRule` 数据权限规则
- 无需额外配置

## 工作原理

### 执行流程
1. **SQL 执行前**：`MappedStatementInterceptor` 拦截器将当前的 `MappedStatement` 存储到 ThreadLocal
2. **数据权限检查**：`AnnotationAwareCreatorDataPermissionRule` 获取当前 MappedStatement
3. **注解检测**：`MethodAnnotationDetector` 检查方法是否标注了 `@NoCreator` 注解
4. **权限应用**：
   - 如果有 `@NoCreator` 注解：跳过 creator 权限控制
   - 如果没有注解：应用正常的 creator 权限控制
5. **清理资源**：SQL 执行完成后，拦截器清理 ThreadLocal

### 缓存机制
- 方法注解检测结果会被缓存，避免重复反射
- 缓存 key：`mappedStatementId`（如：`com.example.mapper.UserMapper.selectById`）
- 缓存 value：`Boolean`（是否有 `@NoCreator` 注解）

## 监控和调试

### 日志配置
```properties
# 启用数据权限相关日志
logging.level.cn.aguyao.module.wsapp.framework.datapermission=DEBUG
```

### 关键日志
- `[MappedStatementInterceptor]` - 拦截器执行日志
- `[MethodAnnotationDetector]` - 注解检测日志
- `[AnnotationAwareCreatorDataPermissionRule]` - 权限规则执行日志

### 缓存监控
```java
// 获取缓存大小
int cacheSize = MethodAnnotationDetector.getCacheSize();

// 清空缓存（用于测试或重新加载）
MethodAnnotationDetector.clearCache();

// 获取指定方法的缓存状态
Boolean cached = MethodAnnotationDetector.getCachedResult("com.example.mapper.UserMapper.selectById");
```

## 最佳实践

### 1. 注解使用原则
- **明确描述**：为每个 `@NoCreator` 注解提供清晰的描述信息
- **谨慎使用**：只在确实需要跳过 creator 权限的场景下使用
- **安全考虑**：对于敏感数据，确保有其他安全控制措施

### 2. 适用场景
- **管理员功能**：管理员需要查看所有数据的场景
- **统计报表**：不需要按创建人过滤的统计查询
- **系统任务**：系统级别的数据处理任务
- **数据导出**：需要导出完整数据的场景

### 3. 性能优化
- 注解检测结果会自动缓存，无需担心性能问题
- 拦截器使用 ThreadLocal，对并发性能影响很小
- 建议在开发环境启用 DEBUG 日志，生产环境使用 INFO 级别

## 兼容性

### 向后兼容
- 完全兼容现有的数据权限配置
- 不影响现有的 `@CreatorPermission` 注解功能
- 现有代码无需修改

### 扩展性
- 可以轻松扩展支持其他类型的权限注解
- 支持自定义权限检查逻辑
- 可以与其他权限框架集成

## 注意事项

1. **注解位置**：`@NoCreator` 注解必须标注在 Mapper 接口的方法上
2. **方法重载**：对于重载方法，注解检测基于方法名，建议避免重载
3. **缓存清理**：在动态修改代码后，可能需要清理缓存
4. **安全风险**：使用 `@NoCreator` 注解会绕过数据权限控制，需要确保安全性
