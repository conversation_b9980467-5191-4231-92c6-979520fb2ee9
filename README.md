# 项目结构说明
## 业务模块
> [aguyao-module-wsapp](aguyao-module-wsapp)
> 
>> [aguyao-module-wsapp-biz](aguyao-module-wsapp/aguyao-module-wsapp-biz)
>>> 1、annotation： 自定义注解
>>>
>>> 2、api： 业务接口，模块件使用
>>>
>>> 3、config： 所有配置文件
>>> 
>>> 4、convert: 转换类
>>>
>>> 5、job: 定时任务
>>>
>>> 6、listener: 监听
>>>
>>> 7、mq: 消息队列（含：rabbitmq）
>>>
>>> 8、websocket: ws


## service 说明
> 1、包apiservice: ws 三方接口
>> 1.1、ApiBasicService: 调三方的基础服务
>>
>> 1.2、LoginAsyncService: ws 登录异步接口
>>
>> 1.3、PrivateBasicService: ws 私聊基础接口
>>
>> 1.4、PrivateFunctionService: ws 私聊功能接口
>>
>> 1.5、GroupBasicService: ws 群基础接口
>>
>> 1.6、GroupFunctionService: ws 群功能接口
> 
> 2、example 所有service层的测试类 全部放这边


## 项目日志
> 1、在类上方，采用注解的方式
> 
> 2、统一使用 @Log4j2
> 


## 其他说明
> application-local.yaml 这个配置文件不要提交


## 客服聊天管理平台
> 代码规范
> 1、controller 统一放在aguyao-module-wsapp-biz这个模块的chat目录下，其他不变
> 2、数据库表，统一以chat开头