package cn.aguyao.framework.web.core.util;

import cn.aguyao.framework.common.enums.TerminalEnum;
import cn.aguyao.framework.common.enums.UserTypeEnum;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.web.config.WebProperties;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 专属于 web 包的工具类
 *
 * <AUTHOR>
 */
@Log4j2
public class WebFrameworkUtils {

    private static final String REQUEST_ATTRIBUTE_LOGIN_USER_ID = "login_user_id";
    private static final String REQUEST_ATTRIBUTE_LOGIN_USER_TYPE = "login_user_type";

    private static final String REQUEST_ATTRIBUTE_COMMON_RESULT = "common_result";

    public static final String HEADER_TENANT_ID = "tenant-id";

    /**
     * 终端的 Header
     *
     * @see cn.aguyao.framework.common.enums.TerminalEnum
     */
    public static final String HEADER_TERMINAL = "terminal";

    private static WebProperties properties;

    // 新增 TransmittableThreadLocal 存储
    private static final TransmittableThreadLocal<HttpServletRequest> requestHolder =
            new TransmittableThreadLocal<>();

    private static final TransmittableThreadLocal<Long> currentUserId = new TransmittableThreadLocal<>();

    public WebFrameworkUtils(WebProperties webProperties) {
        WebFrameworkUtils.properties = webProperties;
    }

    /**
     * 获得租户编号，从 header 中
     * 考虑到其它 framework 组件也会使用到租户编号，所以不得不放在 WebFrameworkUtils 统一提供
     *
     * @param request 请求
     * @return 租户编号
     */
    public static Long getTenantId(HttpServletRequest request) {
        String tenantId = request.getHeader(HEADER_TENANT_ID);
        return NumberUtil.isNumber(tenantId) ? Long.valueOf(tenantId) : null;
    }

    public static void setLoginUserId(ServletRequest request, Long userId) {
        request.setAttribute(REQUEST_ATTRIBUTE_LOGIN_USER_ID, userId);
        requestHolder.get().setAttribute(REQUEST_ATTRIBUTE_LOGIN_USER_ID, userId);
        currentUserId.set(userId);
    }

    /**
     * 设置用户类型
     *
     * @param request 请求
     * @param userType 用户类型
     */
    public static void setLoginUserType(ServletRequest request, Integer userType) {
        request.setAttribute(REQUEST_ATTRIBUTE_LOGIN_USER_TYPE, userType);
        requestHolder.get().setAttribute(REQUEST_ATTRIBUTE_LOGIN_USER_TYPE, userType);
    }

    /**
     * 获得当前用户的编号，从请求中
     * 注意：该方法仅限于 framework 框架使用！！！
     *
     * @param request 请求
     * @return 用户编号
     */
    public static Long getLoginUserId(HttpServletRequest request) {
        if (request == null) {
            return null;
        }

        if(currentUserId.get() != null) {
            return currentUserId.get();
        }

        if (requestHolder.get() != null) {
            Long userId = (Long) requestHolder.get().getAttribute(REQUEST_ATTRIBUTE_LOGIN_USER_ID);
            if (userId != null) {
                return userId;
            }
        }

        return (Long) request.getAttribute(REQUEST_ATTRIBUTE_LOGIN_USER_ID);
    }

    /**
     * 获得当前用户的类型
     * 注意：该方法仅限于 web 相关的 framework 组件使用！！！
     *
     * @param request 请求
     * @return 用户编号
     */
    public static Integer getLoginUserType(HttpServletRequest request) {
        if (request == null) {
            return null;
        }

        Integer userType = null;
        if (requestHolder.get() != null) {
            userType = (Integer) requestHolder.get().getAttribute(REQUEST_ATTRIBUTE_LOGIN_USER_TYPE);
            if (userType != null) {
                return userType;
            }
        }

        // 1. 优先，从 Attribute 中获取
        userType = (Integer) request.getAttribute(REQUEST_ATTRIBUTE_LOGIN_USER_TYPE);
        if (userType != null) {
            return userType;
        }
        // 2. 其次，基于 URL 前缀的约定
        if (request.getServletPath().startsWith(properties.getAdminApi().getPrefix())) {
            return UserTypeEnum.ADMIN.getValue();
        }
        if (request.getServletPath().startsWith(properties.getAppApi().getPrefix())) {
            return UserTypeEnum.MEMBER.getValue();
        }
        if (request.getServletPath().startsWith(properties.getChatApi().getPrefix())) {
            return UserTypeEnum.CUSTSERVICE.getValue();
        }
        return null;
    }

    public static Integer getLoginUserType() {
        HttpServletRequest request = getRequest();
        return getLoginUserType(request);
    }

    public static Long getLoginUserId() {
        HttpServletRequest request = getRequest();
        return getLoginUserId(request);
    }

    public static Integer getTerminal() {
        HttpServletRequest request = getRequest();
        if (request == null) {
            return TerminalEnum.UNKNOWN.getTerminal();
        }
        String terminalValue = request.getHeader(HEADER_TERMINAL);
        return NumberUtil.parseInt(terminalValue, TerminalEnum.UNKNOWN.getTerminal());
    }

    public static void setCommonResult(ServletRequest request, CommonResult<?> result) {
        request.setAttribute(REQUEST_ATTRIBUTE_COMMON_RESULT, result);
    }

    public static CommonResult<?> getCommonResult(ServletRequest request) {
        return (CommonResult<?>) request.getAttribute(REQUEST_ATTRIBUTE_COMMON_RESULT);
    }

    public static HttpServletRequest getRequest() {
        // 优先从 TransmittableThreadLocal 获取
        HttpServletRequest ttlRequest = requestHolder.get();
        if (ttlRequest != null) {
            return ttlRequest;
        }

        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (!(requestAttributes instanceof ServletRequestAttributes)) {
            return null;
        }
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) requestAttributes;
        return servletRequestAttributes.getRequest();
    }

    // 新增请求绑定方法（用于过滤器/拦截器设置）
    public static void setRequest(HttpServletRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Setting request to TransmittableThreadLocal: {}", request);
        }
        requestHolder.set(request);
        // 同时设置到 RequestContextHolder 保持兼容
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
    }

    // 新增请求清除方法（用于请求结束后清理）
    public static void removeRequest() {
        if (log.isDebugEnabled()) {
            log.debug("Removing request from TransmittableThreadLocal");
        }
        currentUserId.remove();
        requestHolder.remove();
        RequestContextHolder.resetRequestAttributes();
    }
}
