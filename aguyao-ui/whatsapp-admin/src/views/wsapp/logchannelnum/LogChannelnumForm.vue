<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="分组ID" prop="groupId">
        <el-input v-model="formData.groupId" placeholder="请输入分组ID" />
      </el-form-item>
      <el-form-item label="代理类型，0：静态；1：动态" prop="proxyType">
        <el-select v-model="formData.proxyType" placeholder="请选择代理类型，0：静态；1：动态">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="执行总数" prop="totalNum">
        <el-input v-model="formData.totalNum" placeholder="请输入执行总数" />
      </el-form-item>
      <el-form-item label="成功数" prop="successNum">
        <el-input v-model="formData.successNum" placeholder="请输入成功数" />
      </el-form-item>
      <el-form-item label="失败数" prop="failNum">
        <el-input v-model="formData.failNum" placeholder="请输入失败数" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { LogChannelnumApi, LogChannelnumVO } from '@/api/wsapp/logchannelnum'
// import { handleTree } from '@/utils/tree'

/** 上号日志 表单 */
defineOptions({ name: 'LogChannelnumForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  groupId: undefined,
  proxyType: undefined,
  totalNum: undefined,
  successNum: undefined,
  failNum: undefined
})
const formRules = reactive({})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await LogChannelnumApi.getLogChannelnum(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as LogChannelnumVO
    if (formType.value === 'create') {
      await LogChannelnumApi.createLogChannelnum(data)
      message.success(t('common.createSuccess'))
    } else {
      await LogChannelnumApi.updateLogChannelnum(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    groupId: undefined,
    proxyType: undefined,
    totalNum: undefined,
    successNum: undefined,
    failNum: undefined
  }
  formRef.value?.resetFields()
}
</script>
