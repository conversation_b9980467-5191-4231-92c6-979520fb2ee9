<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="1000px">
    <div v-loading="loading">
      <!-- 列表 -->
      <el-table :data="list" :stripe="true" :show-overflow-tooltip="true">
        <!-- <el-table-column label="主键" align="center" prop="id" /> -->
        <el-table-column label="发送群id" align="center" prop="receiveGroupId" />
        <el-table-column label="发送群名称" align="center" prop="receiveGroupName" />
        <!-- <el-table-column label="结果" align="center" prop="resultCode" /> -->
        <el-table-column label="结果" align="center" prop="resultInfo" />
        <!-- <el-table-column label="创建时间" align="center" prop="createTime" width="180px" /> -->
      </el-table>
      
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { GroupSendDetailGroupApi, GroupSendDetailGroupVO } from '@/api/wsapp/groupsenddetailgroup'

defineOptions({ name: 'GroupSendDetailGroupDialog' })

const dialogVisible = ref(false)
const dialogTitle = ref('群聊收件人详情')
const loading = ref(false)
const list = ref<GroupSendDetailGroupVO[]>([])
const total = ref(0)
const groupSendDetailId = ref<number>()

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  receiveGroupId: undefined,
  receiveGroupName: undefined,
  resultCode: undefined,
  resultInfo: undefined,
  createTime: []
})

/** 查询列表 */
const getList = async () => {
  if (!groupSendDetailId.value) return
  
  loading.value = true
  try {
    // 根据群发详情ID查询群聊收件人列表
    // 这里需要根据实际API调整查询参数

    const data = await GroupSendDetailGroupApi.getGroupSendDetailGroupPage({
      ...queryParams,
      detailId: groupSendDetailId.value
    })
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 打开弹窗 */
const open = (detailId: number) => {
  dialogVisible.value = true
  groupSendDetailId.value = detailId
  dialogTitle.value = `发送日志`
  resetQuery()
  getList()
}

/** 重置查询 */
const resetQuery = () => {
  queryParams.pageNo = 1
  queryParams.receiveGroupId = undefined
  queryParams.receiveGroupName = undefined
  queryParams.resultCode = undefined
  queryParams.resultInfo = undefined
  queryParams.createTime = []
}

/** 关闭弹窗 */
const close = () => {
  dialogVisible.value = false
  list.value = []
  total.value = 0
}

// 暴露方法给父组件
defineExpose({
  open,
  close
})
</script>
