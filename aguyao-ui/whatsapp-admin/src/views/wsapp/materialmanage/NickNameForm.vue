<template>
  <Dialog title="添加账号昵称" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
       <el-form-item label="账号昵称" prop="fileContent">
        <div style="width: 100%">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :show-file-list="false"
            accept=".txt"
            :on-change="handleFileChange"
            style="margin-bottom: 10px"
          >
            <el-button type="success" :icon="Upload">上传TXT文件</el-button>
          </el-upload>
          <div class="upload-tip">一行一个昵称，回车换行，合行不得超过150</div>
          <el-input
            v-model="formData.fileContent"
            type="textarea"
            :rows="8"
            placeholder="请输入昵称（回车换行）..."
            style="width: 100%; margin-top: 10px"
          />
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { MaterialManageApi, MaterialManageVO } from '@/api/wsapp/materialmanage'
import { Upload } from '@element-plus/icons-vue'
      
/** 分组管理 表单 */
defineOptions({ name: 'NickNameForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') 
const formData = ref({
  fileContent: '',
  materialType: 1
})
const formRules = reactive({
   fileContent: [
    { required: true, message: '请输入文件内容', trigger: 'change' }
  ]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  resetForm()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 文件上传处理
const handleFileChange = (file: any) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    const content = e.target?.result as string
    // 解析文件内容到文本框
    formData.value.fileContent = content
    ElMessage.success('文件上传成功')
  }
  reader.readAsText(file.raw)
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 验证文件内容行数
  if (formData.value.fileContent) {
    const lines = formData.value.fileContent.split('\n').filter(line => line.trim())
    if (lines.length > 150) {
      ElMessage.error('文件内容不能超过150行')
      return
    }
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as MaterialManageVO
     await MaterialManageApi.createMaterial(data)
    message.success(t('common.createSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    fileContent: '',
    materialType: 1
  }
  formRef.value?.resetFields()
}
</script>