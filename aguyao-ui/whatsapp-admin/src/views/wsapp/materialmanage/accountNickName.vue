<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item>
        <el-button type="primary" plain @click="openForm()">
          添加昵称
        </el-button>
        <el-button type="warning" plain @click="batchDelte()">
          批量删除
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading" :data="list" :stripe="true" 
      @selection-change="handleSelectionChange"
      :show-overflow-tooltip="true">
      <el-table-column type="selection" width="55px" />
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="昵称" align="center" prop="materialContent" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180px" :formatter="dateFormatter" />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button link type="danger" @click="handleDelete(scope.row.id)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <NickNameForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import download from '@/utils/download'
import { dateFormatter } from '@/utils/formatTime'
import { MaterialManageApi, MaterialManageVO } from '@/api/wsapp/materialmanage'
import NickNameForm from './NickNameForm.vue'

/** 分组管理 列表 */
defineOptions({ name: 'Material' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<MaterialManageVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  materialType: 1 //账号昵称
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await MaterialManageApi.getMaterialManagePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 添加操作 */
const formRef = ref()
const openForm = () => {
 formRef.value.open()
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await MaterialManageApi.deleteMaterial(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch { }
}

const selectionList = ref([])

const handleSelectionChange = (val: any) => { 
  selectionList.value = val
}
const batchDelte = async () => { 
  try {
    if (selectionList.value.length === 0) {
      message.warning('请先选择要删除的数据');
      return;
    }
    // 删除的二次确认
    await message.delConfirm()

    const ids = selectionList.value.map((item: any) => item.id)

    // 发起删除
    await MaterialManageApi.batchDeleteMaterial( ids )
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {

  }
}
/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
