<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <!-- <el-form-item label="任务编号" prop="taskCode">
        <el-input
          v-model="queryParams.taskCode"
          placeholder="请输入任务编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <!-- <el-form-item label="执行总数" prop="totalCount">
        <el-input
          v-model="queryParams.totalCount"
          placeholder="请输入执行总数"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="已执行" prop="executedCount">
        <el-input
          v-model="queryParams.executedCount"
          placeholder="请输入已执行"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime">
        <el-date-picker
          v-model="queryParams.startTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-date-picker
          v-model="queryParams.endTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item> -->
      <!-- <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item>
        <!-- <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button> -->
        <!-- <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button> -->
        <el-button
          type="success"
          plain
          @click="openForm2('create')"
          v-hasPermi="['wsapp:group-send-record:create']"
        >
          <!-- <Icon icon="ep:plus" class="mr-5px" /> --> 新建群发
        </el-button>
        <!-- <el-button
          type="success"
          plain
          @click="openForm2('record')"
          v-hasPermi="['wsapp:group-send-record:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 群发记录
        </el-button> -->
        <el-button
          type="danger"
          plain
          @click="handleBatchDelete"
          v-hasPermi="['wsapp:group-send-record:delete']"
        >
          <!-- <Icon icon="ep:plus" class="mr-5px" /> --> 批量删除
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['wsapp:group-send-record:export']"
        >
          <!-- <Icon icon="ep:download" class="mr-5px" /> --> 导出
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
v-loading="loading" :data="list" :stripe="true" 
      ref="tableRef"
      :row-key="row => row.id"
      @row-click="handleRowClick"
      :highlight-current-row="true"
      :select-on-indeterminate="false"
      @selection-change="handleSelectionChange"
      :show-overflow-tooltip="true">

      <el-table-column type="selection" width="50" />
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="任务编号" align="center" prop="taskCode" width="200px"/>
      <el-table-column label="执行总数" align="center" prop="totalCount" />
      <el-table-column label="已完成" align="center" prop="executedCount" />
      <el-table-column
        label="开始时间"
        align="center"
        prop="startTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="结束时间"
        align="center"
        prop="endTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="状态" align="center" prop="status" >
        <template #default="scope">
          {{ scope.row.status === 0 ? '未开始' : scope.row.status === 1 ? '进行中' : scope.row.status === 2 ? '已完成' : '未知' }}
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click.stop="openForm('update', scope.row.id)"
            v-hasPermi="['wsapp:group-send-record:update']"
          >
            详情
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['wsapp:group-send-record:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <GroupSendRecordForm ref="formRef" @success="getList" />
  <!-- 新建群发消息 -->
  <CreateForm ref="createFormRef" @success="handleSuccess" />
  <!-- 群发记录弹窗 -->
  <RecordForm ref="recordFormRef" @success="getList" />
    <!-- 群发详情弹窗 -->
  <GroupSendDetailDialog ref="detailDialogRef" />
</template>

<script setup lang="ts">
  import download from '@/utils/download'
  import { dateFormatter } from '@/utils/formatTime'
  import { GroupSendRecordApi, GroupSendRecordVO } from '@/api/wsapp/groupsendrecord'
  import CreateForm from './CreateForm.vue'
  import RecordForm from './RecordForm.vue'
  import GroupSendRecordForm from './GroupSendRecordForm.vue'
  import GroupSendDetailDialog from '../groupsenddetail/GroupSendDetailDialog.vue'
            
/** 群发管理-群发群聊 列表 */
defineOptions({ name: 'GroupSendRecord' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<GroupSendRecordVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  taskCode: undefined,
  totalCount: undefined,
  executedCount: undefined,
  startTime: [],
  endTime: [],
  status: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

///////////// 新建群发消息 ///////////////
const createFormRef = ref()
// 打开弹窗
const openForm2 = (type: string, id?: number) => {
  if (type === 'record') {
      recordFormRef.value.open(type)
    } else if (type === 'create') {
      createFormRef.value.open(type, id)
    }
}

// 成功回调
const handleSuccess = () => {
  // 处理成功后的逻辑，例如刷新列表 todo 
  // debugger
  // 处理成功后的逻辑，刷新列表
  getList()
}

///////////// 群发记录弹窗 ///////////////
 // 群发记录表单引用
 const recordFormRef = ref()

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await GroupSendRecordApi.getGroupSendRecordPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
// 群发详情弹窗引用
const detailDialogRef = ref()
const openForm = (type: string, id?: number) => {
  if (type === 'update') {
    // 打开群发详情弹窗
    detailDialogRef.value.open(id)
  } else {
    formRef.value.open(type, id)
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await GroupSendRecordApi.deleteGroupSendRecord(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await GroupSendRecordApi.exportGroupSendRecord(queryParams)
    download.excel(data, '群发管理-群发群聊.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

// #################################
const selectedRows = ref<GroupSendRecordVO[]>([]) // 选中的行数据

/** 选中行变化时的处理函数 */
const handleSelectionChange = (selection: GroupSendRecordVO[]) => {
  // 可以在这里处理选中的行，例如保存到某个变量
  selectedRows.value = selection
  debugger
}

/** 行点击处理函数 */
const tableRef = ref()
const handleRowClick = (row: GroupSendRecordVO) => {
  // 判断当前行是否已选中
  const isSelected = selectedRows.value.some(item => item.id === row.id)
  if (isSelected) {
    // 取消选中：从 selectedRows 中移除
    selectedRows.value = selectedRows.value.filter(item => item.id !== row.id)
    tableRef.value.toggleRowSelection(row, false)
  } else {
    // 选中：加入 selectedRows
    selectedRows.value.push(row)
    tableRef.value.toggleRowSelection(row, true)
  }
  console.log('Row clicked:', row)
  debugger
}


/** 删除按钮操作 todo todo  */
const handleBatchDelete = async () => {
  try {
    // 删除的二次确认
    await message.delConfirm()

    if (selectedRows.value.length === 0) {
      message.warning('请至少选择一条记录')
      return
    }

    // 获取已选的任务id列表，并拼接成字符串
    const ids = selectedRows.value.map(row => row.id).join(',')
    console.log('Selected IDs:', ids)

    // 发起删除 todo 
    await GroupSendRecordApi.batchDelete(ids)

    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
