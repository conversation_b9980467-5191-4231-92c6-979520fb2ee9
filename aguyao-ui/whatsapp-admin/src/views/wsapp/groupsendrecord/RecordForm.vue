<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="70%">
    <div class="record-header">
      <div class="filter-section">
        <el-select v-model="queryParams.groupId" placeholder="分组" clearable class="mr-2" style="width: 200px;">
          <el-option
            v-for="item in groupList"
            :key="item.id"
            :label="item.groupName"
            :value="item.id"
          />
        </el-select>
        <el-button type="primary" @click="handleQuery" class="mr-2">搜索</el-button>
        <el-button type="danger" @click="handleBatchDelete">按分组删除</el-button>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="recordList"
      :stripe="true"
      :show-overflow-tooltip="true"
      style="width: 100%"
    >
      <!-- <el-table-column label="发送人" align="center" prop="accountId" /> -->
      <el-table-column label="发送人" align="center" prop="accountId" />
      <el-table-column label="群总数" align="center" prop="acctGroupNum" />
      <el-table-column label="完成数量" align="center" prop="finishedNum" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120">
        <template #default="scope">
          <el-button
            link
            type="danger"
            @click="handleDeleteRecord(scope.row.id)"
            v-hasPermi="['wsapp:group-send-record:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- <div v-if="recordList.length === 0" class="empty-data">
      <span>无数据</span>
    </div> -->

    <!-- 分页 -->
    <div class="pagination-container">
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getRecordList"
      />
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { GroupApi, GroupVO } from '@/api/wsapp/group'
import { GroupSendDetailApi } from '@/api/wsapp/groupsenddetail'

/** 群发记录 弹窗 */
defineOptions({ name: 'RecordForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('群发记录') // 弹窗的标题
const loading = ref(false) // 表格加载状态

// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  groupId: undefined,
})

// 记录列表数据
const recordList = ref([])
const total = ref(0)

// 分组选项
// const groupOptions = ref([])

/** 打开弹窗 */
const open = async (type: string) => {
  dialogVisible.value = true
  dialogTitle.value = '群发记录'
  resetQuery()
  await getGroupList() // 获取分组列表
  await getRecordList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗


const groupList = ref<GroupVO[]>([]) // 分组下拉框数据

/** 查询分组列表 */
const getGroupList = async () => {
  try {
    const data = await GroupApi.getGroupList()
    groupList.value = data
    debugger
  } finally {
  }
}


/** 获取记录列表 */
const getRecordList = async () => {
  loading.value = true
  try {
    debugger
    // 这里应该调用实际的API获取群发记录数据
    const data = await GroupSendDetailApi.getGroupSendDetailPage(queryParams)
    recordList.value = data.list
    total.value = data.total
    
    // 模拟数据，实际使用时请替换为API调用
    // setTimeout(() => {
    //   recordList.value = []
    //   total.value = 0
    //   loading.value = false
    // }, 500)
  } catch (error) {
    console.error('获取群发记录失败', error)
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getRecordList()
}

/** 重置查询 */
const resetQuery = () => {
  queryParams.groupId = undefined
  queryParams.pageNo = 1
  queryParams.pageSize = 10
}

/** 导出操作 */
const handleExport = () => {
  message.success('导出功能待实现')
}

/** 打印操作 */
const handlePrint = () => {
  message.success('打印功能待实现')
}

/** 删除单条记录 */
const handleDeleteRecord = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    // await GroupSendRecordApi.deleteGroupSendRecord(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getRecordList()
  } catch {}
}

/** 批量删除操作 */
const handleBatchDelete = async () => {
  if (!queryParams.groupId) {
    message.warning('请先选择分组')
    return
  }
  
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    // await GroupSendRecordApi.batchDeleteGroupSendRecord({ groupId: queryParams.groupId })
    message.success('删除成功')
    // 刷新列表
    await getRecordList()
  } catch {}
}

/** 获取状态类型 */
const getStatusType = (status) => {
  const statusMap = {
    0: 'info',    // 等待中
    1: 'success', // 已完成
    2: 'warning', // 进行中
    3: 'danger'   // 失败
  }
  return statusMap[status] || 'info'
}

/** 获取状态文本 */
const getStatusText = (status) => {
  const statusMap = {
    0: '等待中',
    1: '已完成',
    2: '进行中',
    3: '失败'
  }
  return statusMap[status] || '未知'
}

/** 发送操作成功事件 */
const emit = defineEmits(['success'])
</script>

<style lang="scss" scoped>
.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.filter-section {
  display: flex;
  align-items: center;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: #909399;
  font-size: 14px;
}


.pagination-container {
  display: flex;
  justify-content: right;
  align-items: center;
  padding: 0px 0;
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.pagination-container :deep(.el-pagination) {
  max-width: 100%;
  overflow: hidden;
  flex-wrap: wrap;
}

.pagination-container :deep(.el-pagination .el-pager) {
  flex-wrap: wrap;
}
</style>
