<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="任务编号" prop="taskCode">
        <el-input v-model="formData.taskCode" placeholder="请输入任务编号" />
      </el-form-item>
      <el-form-item label="执行总数" prop="totalCount">
        <el-input v-model="formData.totalCount" placeholder="请输入执行总数" />
      </el-form-item>
      <el-form-item label="已执行" prop="executedCount">
        <el-input v-model="formData.executedCount" placeholder="请输入已执行" />
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime">
        <el-date-picker
          v-model="formData.startTime"
          type="date"
          value-format="x"
          placeholder="选择开始时间"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-date-picker
          v-model="formData.endTime"
          type="date"
          value-format="x"
          placeholder="选择结束时间"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { GroupSendRecordApi, GroupSendRecordVO } from '@/api/wsapp/groupsendrecord'
      
/** 群发管理-群发群聊 表单 */
defineOptions({ name: 'GroupSendRecordForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  taskCode: undefined,
  totalCount: undefined,
  executedCount: undefined,
  startTime: undefined,
  endTime: undefined,
  status: undefined,
})
const formRules = reactive({
  taskCode: [{ required: true, message: '任务编号不能为空', trigger: 'blur' }],
  totalCount: [{ required: true, message: '执行总数不能为空', trigger: 'blur' }],
  executedCount: [{ required: true, message: '已执行不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await GroupSendRecordApi.getGroupSendRecord(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as GroupSendRecordVO
    if (formType.value === 'create') {
      await GroupSendRecordApi.createGroupSendRecord(data)
      message.success(t('common.createSuccess'))
    } else {
      await GroupSendRecordApi.updateGroupSendRecord(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    taskCode: undefined,
    totalCount: undefined,
    executedCount: undefined,
    startTime: undefined,
    endTime: undefined,
    status: undefined,
  }
  formRef.value?.resetFields()
}
</script>