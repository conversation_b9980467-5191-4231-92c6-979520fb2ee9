<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="60%">
    <el-form
      ref="formRef"
      :model="pageData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="选择目标" prop="cascaderSelection">
        <el-cascader
          v-model="pageData.cascaderSelection"
          :options="cascaderOptions"
          :props="cascaderProps"
          placeholder="请选择分组/账号/群组"
          class="w-full"
          collapse-tags
          collapse-tags-tooltip
          filterable
          clearable
          @change="handleCascaderChange"
        />
      </el-form-item>

      <el-form-item label="消息内容" prop="messageContent">
        <div class="message-content-box">
          <p class="text-gray-500 mb-2">支持自定义顺序，比如：先图片后文字，先点击"添加图片"，再点击"添加文字"即可</p>
          
          <div class="message-actions">
            <el-button type="success" plain @click="handleAddText">
              <i class="el-icon-edit mr-1"></i> 添加文字
            </el-button>
            <el-button type="success" plain @click="handleAddImage">
              <i class="el-icon-picture mr-1"></i> 添加图片
            </el-button>
            <el-button type="success" plain @click="handleAddAudio">
              <i class="el-icon-microphone mr-1"></i> 添加语音
            </el-button>
            <el-button type="success" plain @click="handleAddVideo">
              <i class="el-icon-video-camera mr-1"></i> 添加视频
            </el-button>
          </div>
          
          <!-- 按顺序展示内容 -->
          <div v-for="(item, index) in contentOrder" :key="index" class="mt-3">
            <!-- 文字 -->
            <template v-if="item.type === 'text'"> 
              <div>
                <el-input
                  v-model="pageData.messageText"
                  type="textarea"
                  rows="4"
                  placeholder="请输入消息内容"
                />
              </div>
            </template>

            <!-- 图片 -->
            <template v-if="item.type === 'image'">
              <UploadImg v-model="pageData.messageImage" :file-size="5" :file-type="['image/jpeg', 'image/png', 'image/gif']">
                <template #tip>
                  <div class="el-upload__tip">上传图片（图片必须小于5M）</div>
                </template>
              </UploadImg>
            </template>

            <!-- 语音 -->
            <template v-if="item.type === 'audio'">
              <!-- <div class="preview-section">
                <p class="text-gray-600 mb-1">预览图:</p>
                <div class="image-preview">
                  <img :src="pageData.messageAudio" alt="预览图" class="preview-image" />
                </div>
                <div class="upload-section mt-3"> 
                </div>
              </div> -->
            <el-upload
              ref="voiceUploadRef"
              :auto-upload="false"
              :show-file-list="false"
              accept="audio/*"
              :on-change="uploadVoiceToOss"
              style="margin-bottom: 10px"
            >
              <el-button type="primary" :icon="Upload">选择语音文件</el-button>
              <div style="background-color:#fdf6ec;color:#e6a23c;margin-left: 5px;">请上传mp3格式文件</div>
            </el-upload>
            <div class="voice-preview" v-if="pageData.messageAudio">
              <audio :src="pageData.messageAudio" controls style="width: 100%;"></audio>
            </div>

              <!-- <UploadFile
                v-model="pageData.messageAudio"
                :file-size="2" 
                :file-type="['mp3', 'wma', 'wav', 'amr']" 
                :limit="1"
                @change="(file) => handleFileChange(file, 'audio')"
              >
                <template #default>
                  <el-button type="primary"><Icon icon="ep:upload" /> 上传语音</el-button>
                </template>
                <template #tip>
                  <div class="el-upload__tip">格式支持 mp3/wma/wav/amr，文件大小不超过 2M</div>
                </template>
              </UploadFile>
              <div v-if="pageData.messageAudio" class="preview-section mt-2">
                <p class="text-gray-600 mb-1">语音预览:</p>
                <audio controls :src="pageData.messageAudioUrl || pageData.messageAudio"></audio>
              </div> -->
            </template>

            <!-- 视频 -->
            <template v-if="item.type === 'video'">
              <!-- <div class="preview-section">
                <p class="text-gray-600 mb-1">预览图:</p>
                <div class="image-preview">
                  <img :src="pageData.messageVideo" alt="预览图" class="preview-image" />
                </div>
                <div class="upload-section mt-3">
                </div>
              </div> -->

              <UploadFile 
                v-model="pageData.messageVideo"
                :file-size="10" 
                :file-type="['mp4']" 
                :limit="1"
                @change="(file) => handleFileChange(file, 'video')"
              >
                <template #default>
                  <el-button type="primary"><Icon icon="ep:upload" /> 上传视频</el-button>
                </template>
                <template #tip>
                  <div class="el-upload__tip">格式支持 mp4，文件大小不超过 10M</div>
                </template>
              </UploadFile>
              <div v-if="pageData.messageVideo" class="preview-section mt-2">
                <p class="text-gray-600 mb-1">视频预览:</p>
                <video controls width="300" :src="pageData.messageVideoUrl || pageData.messageVideo"></video>
              </div>
            </template>
          </div>
        </div>
      </el-form-item>
      
      <el-form-item label="群间隔时间" prop="intervalTime">
        <div class="interval-time-section flex items-center">
          <el-input-number v-model="pageData.intervalTimeMin" :min="0" :max="100" class="w-24" />
          <span class="mx-2">-</span>
          <el-input-number v-model="pageData.intervalTimeMax" :min="0" :max="100" class="w-24" />
          <span class="ml-2">(秒)</span>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleSubmit" type="primary" :disabled="formLoading">确认发布</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { GroupSendRecordApi, GroupSendRecordVO } from '@/api/wsapp/groupsendrecord'
import { updateFile } from '@/api/infra/file'
import UploadFile from '@/components/UploadFile/src/UploadFile.vue' // 通用文件上传组件
import UploadImg from '@/components/UploadFile/src/UploadImg.vue'   // 图片上传组件
import { GroupApi, GroupVO } from '@/api/wsapp/group'
import { AccountApi, AccountVO } from '@/api/wsapp/account'
import { OssApi } from '@/api/wsapp/oss'

/** 群发管理-群发群聊 表单 */
defineOptions({ name: 'GroupSendRecordForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
// 内容顺序数组，用于按顺序展示不同类型的内容
const contentOrder = ref([])

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('新建群发') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改


// 表单数据
const pageData = ref({
  id: undefined,
  cascaderSelection: [], // 级联选择的值 [[groupId, accountId, groupJid], [groupId2, accountId2, groupJid2]]
  messageText: '', // 消息文本
  messageImage: '', // 消息图片
  messageAudio: '', // 消息语音
  messageAudioUrl: '', // 消息语音实际播放URL
  messageVideo: '', // 消息视频
  messageVideoUrl: '', // 消息视频实际播放URL
  intervalTimeMin: 10, // 群间隔时间最小值
  intervalTimeMax: 30, // 群间隔时间最大值
})

// 级联选择器配置
const cascaderOptions = ref([])
const cascaderProps = {
  multiple: true, // 支持多选
  checkStrictly: true, // 可以选择任意一级
  value: 'id',
  label: 'name',
  children: 'children',
  emitPath: true // 返回完整路径
}

// 表单校验规则
const formRules = reactive({
  cascaderSelection: [{ required: true, message: '请选择分组/账号/群组', trigger: 'change' }],
})

const groupList = ref<GroupVO[]>([]) // 分组下拉框数据

/** 查询分组列表 */
const getGroupList = async () => {
  try {
    const data = await GroupApi.getGroupList()
    groupList.value = data
    await buildCascaderOptions()
  } finally {
  }
}

/** 构建级联选择器数据 */
const buildCascaderOptions = async () => {
  const options = []
  for (const group of groupList.value) {
    const groupOption = {
      id: group.id,
      name: group.groupName,
      children: []
    }
    
    // 获取该分组下的账号列表
    try {
      const accountRes = await AccountApi.getAcctList({ groupOwned: group.id })
      if (accountRes && accountRes.length > 0) {
        for (const account of accountRes) {
          const accountOption = {
            id: account.id,
            name: account.mobile,
            account: account.mobile,
            children: []
          }
          
          // 获取该账号下的群组列表
          try {
            const groupRes = await AccountApi.getAcctGroupList({ account: account.mobile })
            if (groupRes && groupRes.length > 0) {
              accountOption.children = groupRes.map(acctGroup => ({
                id: acctGroup.id,
                name: acctGroup.jname,
                acctGroupJId: acctGroup.jid,
                acctGroupJName: acctGroup.jname
              }))
            }
          } catch (error) {
            console.error('获取账号群组失败:', error)
          }
          
          groupOption.children.push(accountOption)
        }
      }
    } catch (error) {
      console.error('获取账号列表失败:', error)
    }
    
    options.push(groupOption)
  }
  
  cascaderOptions.value = options
}

/** 处理级联选择器变化 */
const handleCascaderChange = (value) => {
  console.log('级联选择器变化:', value)
  // value 是一个数组，包含所有选中的路径
  // 每个路径是一个数组，如 [groupId, accountId, groupJid]
}
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {

  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '新建群发' : '修改群发'
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      pageData.value = await GroupSendRecordApi.getGroupSendRecord(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const handleSubmit = async () => {
  // 校验表单
  await formRef.value.validate()
  
  // 检查是否选择了目标
  if (!pageData.value.cascaderSelection || pageData.value.cascaderSelection.length === 0) {
    message.error('请选择分组/账号/群组')
    return
  }
  
  // 检查是否添加了消息内容
  if (contentOrder.value.length === 0) {
    message.error('请添加消息内容')
    return
  }
  
  // 提交请求
  formLoading.value = true
  try {
    // 构建消息内容数组
    const messageContents = []
    contentOrder.value.forEach(item => {
      if (item.type === 'text' && pageData.value.messageText) {
        messageContents.push({
          type: 'text',
          content: pageData.value.messageText
        })
      } else if (item.type === 'image' && pageData.value.messageImage) {
        messageContents.push({
          type: 'image',
          content: pageData.value.messageImage
        })
      } else if (item.type === 'audio' && pageData.value.messageAudio) {
        messageContents.push({
          type: 'audio',
          content: pageData.value.messageAudio
        })
      } else if (item.type === 'video' && pageData.value.messageVideo) {
        messageContents.push({
          type: 'video',
          content: pageData.value.messageVideo
        })
      }
    })
    
    // 构建目标群组列表
    const targetGroups = []
    debugger
    pageData.value.cascaderSelection.forEach(path => {
      if (path.length === 3) {
        // 完整路径：[groupId, accountId, groupJid]
        const groupId = path[0]
        const accountId = path[1]
        const groupJid = path[2]
        const account = findAccountInCascader(groupId, accountId)
        const acctGroup = account?.children?.find(group => group.id === groupJid)
        
        if (account && acctGroup) {
          targetGroups.push({
            groupId: groupId,
            accountId: accountId,
            groupJid: groupJid,
            account: account.account,
            acctGroupId: acctGroup.id,
            acctGroupJId: acctGroup.acctGroupJId,
            acctGroupJName: acctGroup.acctGroupJName

          })
        }
      } else if (path.length === 2) {
        // 选择到账号级别：[groupId, accountId]
        const groupId = path[0]
        const accountId = path[1]
        const account = findAccountInCascader(groupId, accountId)
        
        if (account && account.children) {
          account.children.forEach(acctGroup => {
            targetGroups.push({
              groupId: groupId,
              accountId: accountId,
              groupJid: acctGroup.id,
              account: account.account,
              acctGroupId: acctGroup.id,
              acctGroupJId: acctGroup.acctGroupJId,
              acctGroupJName: acctGroup.acctGroupJName
            })
          })
        }
      } else if (path.length === 1) {
        // 选择到分组级别：[groupId]
        const groupId = path[0]
        const group = findGroupInCascader(groupId)
        
        if (group && group.children) {
          group.children.forEach(account => {
            if (account.children) {
              account.children.forEach(acctGroup => {
                targetGroups.push({
                  groupId: groupId,
                  accountId: account.id,
                  groupJid: acctGroup.id,
                  account: account,
                  acctGroupId: acctGroup.id,
                  acctGroupJId: acctGroup.acctGroupJId,
                  acctGroupJName: acctGroup.acctGroupJName
                })
              })
            }
          })
        }
      }
    })

    // 构建提交数据
    const submitData = {
      id: pageData.value.id,
      targetGroups: targetGroups,
      messageContents: messageContents,
      intervalTime: `${pageData.value.intervalTimeMin}-${pageData.value.intervalTimeMax}`,
      totalGroups: targetGroups.length,
      intervalTimeMin: pageData.value.intervalTimeMin,
      intervalTimeMax: pageData.value.intervalTimeMax
    } as unknown as GroupSendRecordVO
    
    if (formType.value === 'create') {
      await GroupSendRecordApi.createGroupSendRecord(submitData)
      message.success(t('common.createSuccess'))
    } else {
      await GroupSendRecordApi.updateGroupSendRecord(submitData)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 在级联数据中查找分组 */
const findGroupInCascader = (groupId) => {
  return cascaderOptions.value.find(group => group.id === groupId)
}

/** 在级联数据中查找账号 */
const findAccountInCascader = (groupId, accountId) => {
  const group = findGroupInCascader(groupId)
  if (group && group.children) {
    return group.children.find(account => account.id === accountId)
  }
  return null
}


// 语音和视频上传按钮的点击事件 (如果UploadFile不自动上传)
// const triggerAudioUpload = ref<() => void>()
// const triggerVideoUpload = ref<() => void>()

// 如果 UploadFile 组件的 v-model 直接绑定的是上传后的 URL，则不需要额外的 handleFileChange
// 否则，你可能需要像下面这样处理，或者修改 UploadFile 组件使其更符合当前场景
const handleFileChange = (uploadFile, type: 'image' | 'audio' | 'video') => {
  // ElUpload 的 change 事件会在文件状态改变时触发 (选择文件、上传成功、失败等)
  // 我们主要关心文件成功上传后的状态
  // 如果 UploadFile 组件没有直接提供上传后的 URL，你可能需要在这里手动调用上传
  // 并处理返回结果。但通常 Upload 组件会处理这些。
  // 这里的示例假设 UploadFile 的 v-model 已经是上传后的 URL
  // 因此，主要用于在 pageData 中记录，预览URL可能需要额外处理
  if (uploadFile && uploadFile.status === 'success' && uploadFile.response) {
    const fileUrl = uploadFile.response.data; // 假设接口返回 { code: 0, data: 'url', msg: ''}
    if (type === 'image') {
      pageData.value.messageImage= fileUrl;
    }else if (type === 'video') {
      pageData.value.messageVideo = fileUrl;
    }
  } else if (uploadFile && uploadFile.raw && !uploadFile.status) { // 刚选择文件，还未上传
    // 如果 UploadFile 配置了 auto-upload=false, 你需要在这里手动触发上传
    // 例如: uploadRef.value.submit()
    // 并确保 UploadFile 的 http-request 正确配置为调用我们的 updateFile
    // 为了简化，我们这里假设 UploadFile 内部会处理上传，或者 auto-upload=true
    // 如果使用 auto-upload=true，则 UploadFile 的 :http-request 需要配置
    // const customHttpRequest = async (options) => {
    //   try {
    //     const res = await updateFile({ file: options.file })
    //     options.onSuccess(res) // 将我们接口的返回值传递给 el-upload 的 onSuccess
    //   } catch (err) {
    //     options.onError(err)
    //   }
    // }
    // 然后在 UploadFile 组件上传递 :http-request="customHttpRequest"
  }
}

// 如果需要点击图标播放语音
// const playAudio = (audioSrc: string) => {
//   if (audioSrc) {
//     const audio = new Audio(audioSrc)
//     audio.play()
//   }
// }

/** 重置表单 */
const resetForm = () => {
  pageData.value = {
    id: undefined,
    cascaderSelection: [],
    messageText: '',
    messageImage: '',
    messageAudio: '',
    messageAudioUrl: '',
    messageVideo: '',
    messageVideoUrl: '',
    intervalTimeMin: 10,
    intervalTimeMax: 30,
  }
  // 清空内容顺序
  contentOrder.value = []
  formRef.value?.resetFields()
}


// 根据已有数据恢复内容顺序
const restoreContentOrder = () => {
  contentOrder.value = []
  if (pageData.value.messageText) {
    contentOrder.value.push({ type: 'text' })
  }
  if (pageData.value.messageImage) {
    contentOrder.value.push({ type: 'image' })
  }
  if (pageData.value.messageAudio) {
    contentOrder.value.push({ type: 'audio' })
  }
  if (pageData.value.messageVideo) {
    contentOrder.value.push({ type: 'video' })
  }
}


// 通用文件上传成功后的处理
const handleFileUploadSuccess = async (file: File, type: 'image' | 'audio' | 'video') => {
  try {
    const res = await OssApi.uploadFile(pageData);
    if (type === 'image') {
      pageData.value.messageImage = res
    } else if (type === 'video') {
      debugger;
      pageData.value.messageVideo = res
    }
    message.success('上传成功')
  } catch (error) {
    message.error('上传失败')
    console.error(`Upload ${type} error:`, error)
  }
}

// 上传语音到OSS
const uploadVoiceToOss = async (file) => {
  try {
    const formData = new FormData();
    formData.append('file', file.raw);
    formData.append('folder', 'voice-msg'); 
    formData.append('messageType', 'voice');      
    // 调用OSS上传API
    const res = await OssApi.uploadFile(formData);
    pageData.value.messageAudio = res
  } catch (error) {
    console.error('上传失败:', error);
    ElMessage.error('上传失败，请重试');
     pageData.value.messageAudio = '';
  }
};
/** 添加文字 */
const handleAddText = () => {
  // 检查是否已经添加过文字
  if (!contentOrder.value.some(item => item.type === 'text')) {
    contentOrder.value.push({ type: 'text' })
  } else {
    message.warning('文字内容已添加')
  }
}

/** 添加图片 */
const handleAddImage = () => {
  // 检查是否已经添加过图片
  if (!contentOrder.value.some(item => item.type === 'image')) {
    contentOrder.value.push({ type: 'image' })
  } else {
    message.warning('图片内容已添加')
  }
}

/** 添加语音 */
const handleAddAudio = () => {
  // 检查是否已经添加过语音
  if (!contentOrder.value.some(item => item.type === 'audio')) {
    contentOrder.value.push({ type: 'audio' })
  } else {
    message.warning('语音内容已添加')
  }
}

/** 添加视频 */
const handleAddVideo = () => {
  // 检查是否已经添加过视频
  if (!contentOrder.value.some(item => item.type === 'video')) {
    contentOrder.value.push({ type: 'video' })
  } else {
    message.warning('视频内容已添加')
  }
}

/** 上传文件 */
const handleUploadFile = () => {
  // 实现上传文件的逻辑
  message.success('上传文件功能待实现')
  // 这里可以调用文件上传API，上传成功后设置pageData.value.messageImage
}

// 页面加载时初始化
onMounted(() => {
  getGroupList()
})
</script>

<style lang="scss" scoped>
.select-groups {
  width: 100%;
}

.selected-tags {
  margin-top: 8px;
}

.message-content-box {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background-color: #f9f9f9;
}

.message-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 10px;
}

.preview-section {
  margin-top: 15px;
}

.image-preview {
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  padding: 10px;
  background-color: #fff;
}

.preview-image {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
}

.upload-section {
  margin-top: 15px;
}

.interval-time-section {
  display: flex;
  align-items: center;
}
</style>
