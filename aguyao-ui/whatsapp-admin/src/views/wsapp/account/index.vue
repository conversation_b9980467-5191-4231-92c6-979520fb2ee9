<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
     <el-card class="card-box" shadow="hover"> 
      <el-row :gutter="20">
        <el-col :span="4">
          <div class="statistic-card">
            <div class="statistic-title">账号总数</div>
            <div class="statistic-value">{{ accountStats.total || 0 }}</div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="statistic-card">
            <div class="statistic-title">当前在线</div>
            <div class="statistic-value">{{ accountStats.online || 0 }}</div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="statistic-card">
            <div class="statistic-title">离线</div>
            <div class="statistic-value">{{ accountStats.offline || 0 }}</div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="statistic-card">
            <div class="statistic-title">异常</div>
            <div class="statistic-value">{{ accountStats.abnormal || 0 }}</div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="statistic-card">
            <div class="statistic-title">封禁</div>
            <div class="statistic-value">{{ accountStats.banned || 0 }}</div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="30px"
    >
      <el-row>
        <el-col :span="4">  
          <el-form-item label="" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="账号状态"
              clearable
              class="!w-200px"
            >
              <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.WSAPP_ACCOUNT_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
           <el-form-item label="" prop="versionType">
            <el-select
              v-model="queryParams.versionType"
              placeholder="版本"
              clearable
              class="!w-200px"
            >
              <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.WSAPP_ACCOUNT_VERSION)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
           <el-form-item label="" prop="ipGroup">
            <el-select
              v-model="queryParams.ipGroup"
              placeholder="ip分组"
              clearable
              class="!w-200px"
            >
              <el-option
                v-for="item in ipGroupList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
         <el-col :span="4">
          <el-form-item label="" prop="region">
            <el-select
              v-model="queryParams.region"
              placeholder="选择区域"
              clearable
              class="!w-200px"
            >
              <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.WSAPP_COUNTRY_CODE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
            </el-select>
          </el-form-item>
        </el-col>
         <el-col :span="4">
          <el-form-item label="" prop="groupOwned">
            <el-select
              v-model="queryParams.groupOwned"
              placeholder="选择分组"
              clearable
              class="!w-200px"
            >
              <el-option
                v-for="item in groupList"
                :key="item.id"
                :label="item.groupName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="" prop="tagOwned">
            <el-select
              v-model="queryParams.tagOwned"
              placeholder="选择标签"
              clearable
              class="!w-200px"
            >
              <el-option
                  v-for="item in tagList"
                  :key="item.id"
                  :label="item.tagName"
                  :value="item.id"
                />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-form-item label="" prop="source">
        <el-select
          v-model="queryParams.source"
          placeholder="来源"
          clearable
          class="!w-180px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="" prop="uploadStatus">
        <el-select
          v-model="queryParams.uploadStatus"
          placeholder="上传状态"
          clearable
          class="!w-180px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="" prop="profile">
        <el-select
          v-model="queryParams.profile"
          placeholder="头像"
          clearable
          class="!w-180px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item> -->
      <el-row>
        <el-col :span="4">
          <el-form-item label="" prop="otherInfo">
            <el-input
              v-model="queryParams.otherInfo"
              placeholder="账号/昵称/ip"
              clearable
              class="!w-200px"
            />
          </el-form-item>
        </el-col>
        <el-col :offset="16" :span="4">
          <el-form-item>
            <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px !w-29px" /> 搜索</el-button>
            <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px !w-29px" /> 重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-row>
        <el-col :span="8">
           <el-form-item class="right-item">
            <el-button
              type="warning"
              round
              @click="handleLogin"
              v-hasPermi="['wsapp:account:create']"
              class="!w-94px"
            >
              离线登录
            </el-button>
            <el-button
              type="info"
              round
              @click="scanQrcodeLogin"
              v-hasPermi="['wsapp:account:create']"
              class="!w-94px"
            >
              扫码登录
            </el-button>
            <!-- <el-button
              type="primary"
              round
              @click="handleGetAcctGroup"
              :loading="exportLoading"
              v-hasPermi="['wsapp:account:create']"
              class="!w-94px"
            >
              获取群组
            </el-button> -->
          </el-form-item>
        </el-col>
        <el-col :offset="12" :span="4">
          <el-form-item >
            <el-select
              v-model="queryParams.batchOperationType"
              placeholder="批量操作账号"
              class="!w-200px custom-select"
              @change="operAccount"
              @visible-change="handleDropdownVisibleChange"
            >
              <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.WSAPP_ACCOUNT_OPERTYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
v-loading="loading" :data="list" :stripe="true" 
      @selection-change="handleSelectionChange" :show-overflow-tooltip="true">
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="端口编号" align="center" prop="portId" />
      <el-table-column label="账号" align="center" prop="mobile" width="150"/>
      <el-table-column label="昵称" align="center" prop="nickname" width="150"/>
      <el-table-column label="ip" align="center" prop="ipAddress" />
      <el-table-column label="区域" align="center" prop="region">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.WSAPP_COUNTRY_CODE" :value="scope.row.region" />
        </template>
      </el-table-column>
      <el-table-column label="类型" align="center" prop="proxyType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.WSAPP_PROXY_TYPE" :value="scope.row.proxyType" />
        </template>
      </el-table-column>
      <el-table-column label="二维码" prop="qrCodeUrl">
        <template #default="scope">
          <el-tooltip
            effect="dark"
            content="点击放大">
            <div @click="handleQrClick(scope.row)" >
              <Icon icon="tdesign:qrcode" :size="30" color="#b134f1"/>
            </div>
          </el-tooltip>
        </template>
      </el-table-column>  
      <el-table-column label="好友/群" align="center" prop="friendsGroups" />
      <el-table-column label="所属分组" align="center" prop="groupName" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.WSAPP_ACCOUNT_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>  
      <!-- <el-table-column label="来源" align="center" prop="source" /> -->
      <el-table-column
        label="登录时间"
        align="center"
        prop="updateTime"
        width="180px"
        :formatter="dateFormatter"
      />
      <!-- <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['wsapp:account:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['wsapp:account:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column> -->
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <AccountForm ref="formRef" @success="getList" />
   <!-- 放大弹窗：显示大尺寸二维码 -->
  <el-dialog
    v-model="dialogVisible"
    title="二维码详情"
    width="550px"
    :close-on-click-modal="false"
  >
    <div class="dialog-content">
      <!-- 大尺寸二维码 -->
      <span v-text="currentQrValue" v-if="!QrcodeVisible" style="color: red; font-size: 30px;"></span>
      <Qrcode :text="currentQrValue" :width="500" v-if="QrcodeVisible"/>
    </div>
    <template #footer>
      <el-button @click="dialogVisible = false">关闭</el-button>
    </template>
  </el-dialog>
  <!-- 设置分组弹窗 -->
  <el-dialog
    v-model="dialogGroupVisible"
    title="设置分组"
    width="550px"
    :close-on-click-modal="false"
  >
    <div class="dialog-content">
      <el-form
        ref="groupRef"
        :model="groupForm"
        :rules="groupFormRules"
        label-width="100px"
      >
        <el-form-item label="分组名称" prop="groupOwned">
          <el-select
              v-model="groupForm.groupOwned"
              placeholder="选择分组"
              clearable
              class="!w-200px"
            >
              <el-option
                v-for="item in groupList"
                :key="item.id"
                :label="item.groupName"
                :value="item.id"
              />
            </el-select>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="setAccountGroup" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogGroupVisible = false">关 闭</el-button>
    </template>
  </el-dialog>
  <!-- 设置标签弹窗 -->
  <el-dialog
    v-model="dialogTagVisible"
    title="设置标签"
    width="550px"
    :close-on-click-modal="false"
  >
    <div class="dialog-content">
      <el-form
        ref="tagRef"
        :model="tagForm"
        :rules="tagFormRules"
        label-width="100px"
      >
        <el-form-item label="标签名称" prop="tagOwned">
          <el-select
              v-model="tagForm.tagOwned"
              placeholder="选择标签"
              clearable
              class="!w-200px"
            >
              <el-option
                v-for="item in tagList"
                :key="item.id"
                :label="item.tagName"
                :value="item.id"
              />
            </el-select>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="setAccountTag" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogTagVisible = false">关 闭</el-button>
    </template>
  </el-dialog>
  <!-- 扫码登录弹框 -->
  <el-dialog
    v-model="dialogScanVisible"
    title="扫码登录"
    width="450px"
    :close-on-click-modal="false"
  >
    <div class="dialog-content">
      <el-form
        ref="scanRef"
        :model="scanForm"
        :rules="scanFormRules"
        label-width="100px"
      >
        <el-form-item label="设置标签" prop="tagOwned">
          <el-select
              v-model="scanForm.tagOwned"
              placeholder="选择标签"
              clearable
              class="!w-300px"
            >
              <el-option
                v-for="item in tagList"
                :key="item.id"
                :label="item.tagName"
                :value="item.id"
              />
            </el-select>
        </el-form-item>
        <el-form-item label="设置分组" prop="groupOwned">
          <el-select
              v-model="scanForm.groupOwned"
              placeholder="选择分组"
              clearable
              class="!w-300px"
            >
              <el-option
                v-for="item in groupList"
                :key="item.id"
                :label="item.groupName"
                :value="item.id"
              />
            </el-select>
        </el-form-item>
         <el-form-item label="系统类型" prop="systemType">
          <el-radio-group v-model="scanForm.systemType">
            <el-radio :label="1">安卓</el-radio>
            <el-radio :label="2">IOS</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="版本类型" prop="versionType">
          <el-radio-group v-model="scanForm.versionType">
            <el-radio :label="1">个人号</el-radio>
            <el-radio :label="2">商业版</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="代理类型" prop="proxyType" style="display: flex; justify-content: space-between; ">
          <el-radio-group v-model="scanForm.proxyType" @change="getIpGroupList">
            <el-radio :label="1">动态IP</el-radio>
            <el-radio :label="2">静态IP</el-radio>
          </el-radio-group>
          <el-select
              v-if="scanForm.proxyType == 2" v-model="scanForm.ipGroup" placeholder="请选择自定义IP分组"
              clearable class="mr-2" style="margin-left: 20px; flex: 1">
              <el-option
                v-for="item in ipGroupList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select> 
        </el-form-item>
      </el-form>
      <el-button
        type="primary"
        round
        @click="getQrcodeLogin"
        style="float: right;margin-right: 20px;margin-top: -15px;"
        :disabled="formLoading"
      >
        获取二维码
      </el-button>
      <div class="scan-right-box">
        <Qrcode :text="loginQrValue" :width="370" />
      </div>
    </div>
    <template #footer>
      <el-button @click="dialogScanVisible = false">关 闭</el-button>
    </template>
  </el-dialog>
   <!-- 更换昵称弹框 -->
  <el-dialog
    v-model="dialogNickVisible"
    title="更换昵称"
    width="450px"
    :close-on-click-modal="false"
  >
    <div class="dialog-content">
      <el-form
        ref="nickRef"
        :model="nickForm"
        label-width="100px"
      >
         <el-form-item label="" prop="type" label-width="30">
          <el-radio-group v-model="nickForm.type">
            <el-radio :label="1">素材（随机）</el-radio>
            <!-- <el-radio :label="2">素材（顺序）</el-radio> -->
            <el-radio :label="2">自定义</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="nickForm.type == 2" label="账号昵称" prop="groupPrefix">
          <el-input v-model="nickForm.nickname" placeholder="请输入账号昵称" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="setAccountNickName" type="primary" :disabled="formLoading">提 交</el-button>
      <el-button @click="dialogNickVisible = false">关 闭</el-button>
    </template>
  </el-dialog>
   <!-- 更换个人关于弹框 -->
  <el-dialog
    v-model="dialogAboutVisible"
    title="修改关于"
    width="450px"
    :close-on-click-modal="false"
  >
    <div class="dialog-content">
      <el-form
        ref="aboutRef"
        :model="aboutForm"
        label-width="100px"
      >
         <el-form-item label="" prop="type" label-width="30">
          <el-radio-group v-model="aboutForm.type">
            <el-radio :label="1">素材（随机）</el-radio>
            <!-- <el-radio :label="2">素材（顺序）</el-radio> -->
            <el-radio :label="2">自定义</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="aboutForm.type == 2" label="个人关于" prop="groupPrefix">
          <el-input v-model="aboutForm.nickname" placeholder="请输入关于" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="setAccountNickName" type="primary" :disabled="formLoading">提 交</el-button>
      <el-button @click="dialogAboutVisible = false">关 闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import download from '@/utils/download'
  // import { reactive, ref, onMounted } from 'vue' // 确保导入 
  import { AccountApi, AccountVO, ChannelLoginVO } from '@/api/wsapp/account'
  import { dateFormatter } from '@/utils/formatTime'
  import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
  import { Qrcode } from '@/components/Qrcode'
  import { IpGroupManagerApi, IpGroupManagerVO } from '@/api/wsapp/ipgroupmanager'
  import { GroupApi, GroupVO } from '@/api/wsapp/group'
  import { TagApi, TagVO } from '@/api/wsapp/tag'

const ipGroupList = ref<IpGroupManagerVO[]>([]) // IP分组下框数据
const getIpGroupList = async () => {
  // 调用API查询列表
  const data = {
    name: ''
  }
  const res = await IpGroupManagerApi.getIpGroupManagerList(data);
  if (res) {
    ipGroupList.value = res
  }
}

const groupList = ref<GroupVO[]>([]) // 分组下拉框数据
/** 查询分组列表 */
const getGroupList = async () => {
  try {
    const data = await GroupApi.getGroupList()
    groupList.value = data
    debugger
  } finally {
  }
}

const tagList = ref<TagVO[]>([]) // 标签下拉框数据
/** 查询标签列表 */
const getTagList = async () => {
  try {
    const data = await TagApi.getTagList()
    tagList.value = data
    debugger
  } finally {
  }
}

  // 弹窗相关状态
const dialogVisible = ref(false); // 控制弹窗显示/隐藏
const QrcodeVisible = ref(false);
const dialogGroupVisible = ref(false); // 控制弹窗显示/隐藏
const dialogTagVisible = ref(false); // 控制弹窗显示/隐藏
const dialogScanVisible = ref(false); // 控制弹窗显示/隐藏
const dialogNickVisible = ref(false);
const dialogAboutVisible = ref(false);
const currentQrValue = ref('');  // 存储当前点击的二维码内容
const loginQrValue = ref('');  // 存储当前点击的二维码内容

// 点击二维码时的处理函数
const handleQrClick = async (row) => {
  currentQrValue.value = ''; 
  QrcodeVisible.value = false;
  dialogVisible.value = true;     // 显示弹窗
  try {
    const res = await AccountApi.getQrcodeBymobile(row.mobile);
    console.log(res)
    if (res) {
      currentQrValue.value = res; // 记录当前二维码内容
      QrcodeVisible.value = true;
    } else {
      currentQrValue.value = '账号不在线'; 
      QrcodeVisible.value = false;
    }
    } finally {
      
  }
  
};

/** 账号管理 列表 */
defineOptions({ name: 'Account' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

// 定义用于存储账号统计数据的响应式对象
const accountStats = reactive({
  total: 0,
  online: 0,
  offline: 0,
  abnormal: 0,
  banned: 0
})

// 模拟获取统计数据，您需要替换为实际的API调用
const getAccountStats = async () => {
   try {
    const res = await AccountApi.getAccountNum();
    if (res) {
      accountStats.total = res.total;
      accountStats.online = res.online;
      accountStats.offline = res.offline;
      accountStats.abnormal = res.abnormal;
      accountStats.banned = res.banned;
    } else {
      message.error('操作失败')
    }
    
  } finally {
  }
}

const loading = ref(true) // 列表的加载中
const list = ref<AccountVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  device: undefined,
  nickname: undefined,
  account: undefined,
  ipAddress: undefined,
  region: undefined,
  type: undefined,
  friendsGroups: undefined,
  groupOwned: undefined,
  status: undefined,
  source: undefined,
  updateTime: [],
  versionType: undefined,
  uploadStatus: undefined,
  ipGroup: undefined,
  profile: undefined,
  group: undefined,
  tagOwned: undefined,
  otherInfo:  undefined,
  batchOperationType: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await AccountApi.getAccountPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
  getAccountStats()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await AccountApi.deleteAccount(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await AccountApi.exportAccount(queryParams)
    download.excel(data, '账号管理.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }

}
/** 登录 */
const handleLogin = async () => {
  const values = selectionList.value
  if (values.length <= 0) {
    message.warning("请先选择账号");
    return 
  }
  // 获取所选账号的ID数组
  const accountIds = values.map(item => item.id)
  console.log('选中的账号ID:', accountIds)
  
  try {
    const res = await AccountApi.batchLogin(accountIds);
    // console.log('登录结果:', res)
    if (res) {
      message.success('操作成功')
      selectionList.value = []
      getList()
      getAccountStats()
    } else {
      message.error('操作失败')
    }
    
  } finally {
  }
}

/** 扫码登录 */
const scanQrcodeLogin = () => {
  dialogScanVisible.value = true
  resetScanForm();
}

/** 更换昵称 */
const setNick = () => {
  dialogNickVisible.value = true
  resetNickForm();
}

/** 修改关于*/
const setAbout = () => {
  dialogAboutVisible.value = true
  resetAboutForm();
}


const selectionList = ref<AccountVO[]>([])

const handleSelectionChange = (val: AccountVO[]) => { 
  selectionList.value = val
}

/** 获取群组 */
const handleGetAcctGroup = async () => {
  const values = selectionList.value
  // 获取所选账号的ID数组
  const accountIds = values.map(item => item.id)
  
  try {
    const res = await AccountApi.batchAcctGroup(accountIds);
    if (res) {
      message.success('操作成功')
      selectionList.value = []
      getList()
    } else {
      message.error('操作失败')
    }
    
  } finally {
  }
}
const handleDropdownVisibleChange = (visible) => {
  if (visible) { // 当下拉框打开时
     queryParams.batchOperationType = undefined; // 清空选中值
  }
}
/** 批量操作账号 */
const operAccount = async () => {
  const values = selectionList.value
  if (values.length <= 0) {
    message.warning("请先选择账号");
    return 
  }
  // 获取所选账号的ID数组
  const accountIds = values.map(item => item.id)
  console.log('选中的账号ID:', accountIds)
  //1下线账号,2删除账号
  if (queryParams.batchOperationType == 1 || queryParams.batchOperationType == 2) {
  try {
      const res = await AccountApi.batchOperAccountByType(accountIds,queryParams.batchOperationType);
      if (res) {
        message.success('操作成功')
        selectionList.value = []
        getList()
        getAccountStats()
      } else {
        message.error('操作失败')
      }
      
    } finally {
    }
  } else if (queryParams.batchOperationType == 3) { //设置分组
    dialogGroupVisible.value = true;
  } else if (queryParams.batchOperationType == 4) { //设置标签
    dialogTagVisible.value = true;
  } else if (queryParams.batchOperationType == 5) { //更换头像

  } else if (queryParams.batchOperationType == 6) { //更换昵称
     setNick()
  } else if (queryParams.batchOperationType == 7) { //修改关于
     setAbout()
  } else if (queryParams.batchOperationType == 22) { //更新群组
     handleGetAcctGroup()
  }
}
const groupRef = ref() // 表单 Ref
const tagRef = ref() // 表单 Ref
const formLoading = ref(false) // 表单的加载中：1）提交的按钮禁用
const groupForm = ref({
  groupOwned: undefined
})
const groupFormRules = reactive({
  groupOwned: [{ required: true, message: '分组名称不能为空', trigger: 'blur' }],
})
const tagForm = ref({
  tagOwned: undefined
})
const tagFormRules = reactive({
  tagOwned: [{ required: true, message: '标签名称不能为空', trigger: 'blur' }],
})
const scanForm = ref({
  groupOwned: undefined,
  tagOwned: undefined,
  systemType: 1,
  versionType: 1,
  proxyType: 1,
  ipGroup: undefined
})
const scanFormRules = reactive({
  tagOwned: [{ required: true, message: '标签名称不能为空', trigger: 'blur' }],
  groupOwned: [{ required: true, message: '分组名称不能为空', trigger: 'blur' }],
})
const setAccountGroup = async () => {
  // 校验表单
  await groupRef.value.validate()
  // 提交请求
  formLoading.value = true
  const values = selectionList.value
  if (values.length <= 0) {
    message.warning("请先选择账号");
    return 
  }
  // 获取所选账号的ID数组
  const accountIds = values.map(item => item.id)
  try {
      const res = await AccountApi.batchSetAccountInfo(accountIds,groupForm.value.groupOwned,1)
      if (res) {
        message.success('操作成功')
        selectionList.value = []
        getList()
        getAccountStats()
      } else {
        message.error('操作失败')
      }
  } finally {
    formLoading.value = false
    dialogGroupVisible.value = false
    groupForm.value = {
      groupOwned: undefined
    }
  }
}
const setAccountTag = async () => {
  // 校验表单
  await tagRef.value.validate()
  // 提交请求
  formLoading.value = true
  const values = selectionList.value
  if (values.length <= 0) {
    message.warning("请先选择账号");
    return 
  }
  // 获取所选账号的ID数组
  const accountIds = values.map(item => item.id)
  try {
     const res = await AccountApi.batchSetAccountInfo(accountIds,tagForm.value.tagOwned,2);
      if (res) {
        message.success('操作成功')
        selectionList.value = []
        getList()
        getAccountStats()
      } else {
        message.error('操作失败')
      }
  } finally {
    formLoading.value = false
    dialogTagVisible.value = false
    tagForm.value = {
      tagOwned: undefined,
    }
  }
}
/** 获取二维码 */
const getQrcodeLogin = async () => {
  try {
    const data = scanForm.value as unknown as ChannelLoginVO
    formLoading.value = true
     try {
     const res = await AccountApi.getQrcodeLogin(data);
      if (res) {
        loginQrValue.value = res; 
      }
  } finally {
    formLoading.value = false
  }
  } catch {}
}

/** 初始化 **/
onMounted(() => {
  getList()
  getAccountStats() // 在组件挂载后获取统计数据
  getIpGroupList()
  getGroupList()
  getTagList()
})
const nickForm = ref({
 nickname: '', 
 type: 1,
 operType: 1
})
const aboutForm = ref({
 nickname: '', 
 type: 1,
 operType: 2
})
const scanRef = ref() // 搜索的表单
// 重置表单
const resetScanForm = () => {
  scanRef.value.resetFields()
   // 手动重置所有字段
  Object.assign(scanForm, {
    groupOwned: undefined,
    tagOwned: undefined,
    systemType: 1,
    versionType: 1,
    proxyType: 1,
    ipGroup: undefined
  })
}

const nickRef = ref() // 
// 重置表单
const resetNickForm = () => {
  nickRef.value.resetFields()
   // 手动重置所有字段
  Object.assign(nickForm, {
    nickname: '',
    type: 1,
    operType: 1
  })
}
const aboutRef = ref() // 
// 重置表单
const resetAboutForm = () => {
  aboutRef.value.resetFields()
   // 手动重置所有字段
  Object.assign(nickForm, {
    nickname: '',
    type: 1,
    operType: 2
  })
}
// 设置昵称
const setAccountNickName = async () => {
  // 校验表单
  await nickRef.value.validate()
  // 提交请求
  formLoading.value = true
  const values = selectionList.value
  if (values.length <= 0) {
    message.warning("请先选择账号");
    return 
  }
  // 获取所选账号的ID数组
  const accountIds = values.map(item => item.id)
  try {
     const res = await AccountApi.batchSetAccountProfile(accountIds,nickForm.value.nickname,nickForm.value.type,1);;
      if (res) {
        message.success('操作成功')
        selectionList.value = []
        getList()
        getAccountStats()
      } else {
        message.error('操作失败')
      }
  } finally {
    formLoading.value = false
    dialogNickVisible.value = false
    resetNickForm()
  }
}
// 设置昵称
const setAccountAbout = async () => {
  // 校验表单
  await aboutRef.value.validate()
  // 提交请求
  formLoading.value = true
  const values = selectionList.value
  if (values.length <= 0) {
    message.warning("请先选择账号");
    return 
  }
  // 获取所选账号的ID数组
  const accountIds = values.map(item => item.id)
  try {
     const res = await AccountApi.batchSetAccountProfile(accountIds,nickForm.value.nickname,nickForm.value.type,2);;
      if (res) {
        message.success('操作成功')
        selectionList.value = []
        getList()
        getAccountStats()
      } else {
        message.error('操作失败')
      }
  } finally {
    formLoading.value = false
    dialogAboutVisible.value = false
    resetAboutForm()
  }
}
</script>

<style scoped lang="scss">
  .card-box {
    margin-bottom: 15px;
  }

  .statistic-card {
  text-align: center;
  padding: 10px;
}

.statistic-title {
  font-size: 14px;
  color: #b8bbc0;
  margin-bottom: 5px;
}

.statistic-value {
  font-size: 20px;
  font-weight: bold;
  color: #9ea2aa;
}
::v-deep .custom-select .el-select__wrapper {
  background-color: #009688; /* 浅绿色背景 */
  border-color: #009688;
}
::v-deep .custom-select .el-select__placeholder {
  color:white
}
::v-deep .custom-select .el-select__caret {
  color:white
}
.scan-right-box {
  margin-left: 30px;
  margin-top: 50px;
  width: 370px;
  height: 370px;
  border: 1px solid #d7dae2;
  border-radius: 5px;
}
</style>
