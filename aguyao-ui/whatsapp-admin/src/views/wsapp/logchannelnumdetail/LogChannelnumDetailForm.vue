<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="日志主表ID" prop="logId">
        <el-input v-model="formData.logId" placeholder="请输入日志主表ID" />
      </el-form-item>
      <el-form-item label="代理类型，0：静态；1：动态" prop="proxyType">
        <el-select v-model="formData.proxyType" placeholder="请选择代理类型，0：静态；1：动态">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态，1：进行中；2：已完成" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="结果，1：成功；2：失败" prop="result">
        <el-input v-model="formData.result" placeholder="请输入结果，1：成功；2：失败" />
      </el-form-item>
      <el-form-item label="返回信息" prop="resultInfo">
        <el-input v-model="formData.resultInfo" placeholder="请输入返回信息" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { LogChannelnumDetailApi, LogChannelnumDetailVO } from '@/api/wsapp/logchannelnumdetail'

// import { handleTree } from '@/utils/tree'

/** 上号日志子 表单 */
defineOptions({ name: 'LogChannelnumDetailForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  logId: undefined,
  proxyType: undefined,
  status: undefined,
  result: undefined,
  resultInfo: undefined
})
const formRules = reactive({})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await LogChannelnumDetailApi.getLogChannelnumDetail(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as LogChannelnumDetailVO
    if (formType.value === 'create') {
      await LogChannelnumDetailApi.createLogChannelnumDetail(data)
      message.success(t('common.createSuccess'))
    } else {
      await LogChannelnumDetailApi.updateLogChannelnumDetail(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    logId: undefined,
    proxyType: undefined,
    status: undefined,
    result: undefined,
    resultInfo: undefined
  }
  formRef.value?.resetFields()
}
</script>
