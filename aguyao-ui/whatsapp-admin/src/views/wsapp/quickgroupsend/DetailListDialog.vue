<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="70%">
    <!-- 表格区域 -->
    <el-table 
      v-loading="loading" 
      :data="list" 
      stripe 
      border
      style="width: 100%"
    >
      <el-table-column label="加粉账号" align="center" prop="account" min-width="120" />
      <el-table-column label="好友账号" align="center" prop="mobile" min-width="120" />
      <el-table-column label="开始时间" align="center" prop="createTime" min-width="150">
        <template #default="scope">
          {{ formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="返回说明" align="center" prop="resultInfo" min-width="180" />
    </el-table>

    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
      class="mt-4"
    />
    
    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <!-- <el-button @click="close">关 闭</el-button> -->
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { formatDate } from '@/utils/formatTime'
import { QuickGroupSendDetailApi, type QuickGroupSendDetailVO } from '@/api/wsapp/quickgroupsenddetail'

interface ListItem {
  id?: number
  account: string
  friendAccount: string
  startTime: string
  description: string
}

interface QueryParams {
  pageNo: number
  pageSize: number
  [key: string]: any
}

defineOptions({ name: 'SimpleListDialog' })

const dialogVisible = ref(false)
const dialogTitle = ref('查看详情')
const loading = ref(false)
const list = ref<ListItem[]>([])
const total = ref(0)

// 查询参数
const queryParams = ref<QueryParams>({
  pageNo: 1,
  pageSize: 10
})

/** 打开弹窗 */
const open = async (title?: string, params?: any) => {
  dialogVisible.value = true
  if (title) {
    dialogTitle.value = title
  }
  
  // 如果传入了额外参数，合并到查询参数中
  if (params) {
    Object.assign(queryParams.value, params)
  }
  
  // 重置分页
  queryParams.value.pageNo = 1
  
  // 获取数据
  await getList()
}

/** 关闭弹窗 */
const close = () => {
  dialogVisible.value = false
  // 重置数据
  list.value = []
  total.value = 0
  queryParams.value = {
    pageNo: 1,
    pageSize: 10
  }
}

/** 获取列表数据 */
const getList = async () => {
  loading.value = true
  try {
    // 这里替换为实际的API调用
    const data = await QuickGroupSendDetailApi.getQuickGroupSendDetailPage(queryParams.value)
    debugger

    list.value = data.list
    total.value = data.total

    // 模拟数据
    // const mockData = {
    //   list: [
    //     {
    //       id: 1,
    //       account: '************',
    //       friendAccount: '*************',
    //       startTime: '2025-07-14 10:32:06',
    //       description: '文字消息已送达，登录失效，账号登录后，请稍后重试'
    //     },
    //     {
    //       id: 2,
    //       account: '************',
    //       friendAccount: '*************',
    //       startTime: '2025-07-14 10:33:01',
    //       description: '文字消息已送达，登录失效，账号登录后，请稍后重试'
    //     },
    //     {
    //       id: 3,
    //       account: '************',
    //       friendAccount: '*************',
    //       startTime: '2025-07-14 10:36:01',
    //       description: '账号封禁'
    //     }
    //   ],
    //   total: 3
    // }
    
    // list.value = mockData.list
    // total.value = mockData.total
  } catch (error) {
    console.error('获取数据失败：', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 暴露方法供父组件调用
defineExpose({ open, close })
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 25px;
}

.mt-4 {
  margin-top: 25px;
}
</style>
