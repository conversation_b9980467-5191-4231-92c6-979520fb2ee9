<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="账号" prop="account">
        <el-input v-model="formData.account" placeholder="请输入账号" :disabled="isDisabled"/>
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input v-model="formData.password" :placeholder="tipInfo" />
      </el-form-item>
      <el-form-item label="批量添加" prop="batchAdd">
        <el-radio-group v-model="formData.batchAdd">
          <el-radio :value="0">否</el-radio>
          <el-radio :value="1">是</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="IP分组" prop="ipGroup">
        <el-input v-model="formData.ipGroup" placeholder="请输入IP分组" />
      </el-form-item>
      <el-form-item label="群翻译" prop="groupTranslation">
        <el-switch
          v-model="formData.groupTranslation"
          :active-value="1"
          :inactive-value="0"
          active-text="开启"
          inactive-text="关闭"
          @change="handleGroupTranslationChange"
        />
      </el-form-item>
      <el-form-item label="进入翻译" prop="entryTranslation">
        <!-- <el-input v-model="formData.entryTranslation" placeholder="请输入进入翻译" /> -->
         <el-select
            v-model="formData.entryTranslation"
            placeholder="选择进入翻译"
            clearable
            :disabled="entryTranslationDisabled"
          >
            <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.CS_ACCOUNT_TRANSLATE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
          </el-select>
      </el-form-item>
      <el-form-item label="发出翻译" prop="exitTranslation">
        <!-- <el-input v-model="formData.exitTranslation" placeholder="请输入发出翻译" /> -->
         
          <el-select
            v-model="formData.exitTranslation"
            placeholder="选择发出翻译"
            clearable
            :disabled="entryTranslationDisabled"
            @change="handleExitTranslationChange"
          >
            <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.CS_ACCOUNT_TRANSLATE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
          </el-select>
      </el-form-item>
      
      <el-form-item label="单日取号" prop="dailyFetchLimit">
        <el-input-number
          v-model="formData.dailyFetchLimit"
          placeholder="请输入单日取号数量（0为不限制）"
          style="width: 100%"
        />
      </el-form-item>

    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { CustServiceAccountApi, CustServiceAccountVO } from '@/api/wsapp/custserviceaccount'

/** 客服账号信息表 表单 */
defineOptions({ name: 'CustServiceAccountForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  account: undefined,
  password: undefined,
  managementGroup: undefined,
  ipGroup: undefined,
  entryTranslation: undefined,
  exitTranslation: undefined,

  tempEntryTranslation: undefined,
  tempExitTranslation: undefined,

  dailyFetchLimit: undefined,
  newFriends: undefined,
  accountRegistration: undefined,
  friendTransfer: undefined,
  batchAdd: 0,
  groupTranslation: undefined
})
const formRules = computed(() => ({
  account: [{ required: true, message: '账号不能为空', trigger: 'blur' }],
  password: formType.value === 'create' 
    ? [{ required: true, message: '密码不能为空', trigger: 'blur' }]
    : [], 
}))
const formRef = ref() // 表单 Ref
const tipInfo = ref('密码') // 密码
const isDisabled = ref(false) // 是否禁用账号输入框

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  if ('create' == type) {
    tipInfo.value = '密码'
    isDisabled.value = false
  } else {
    tipInfo.value = '留空不修改'
    isDisabled.value = true
  }

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CustServiceAccountApi.getCustServiceAccount(id)

        if (formData.value.groupTranslation === 1) {
          entryTranslationDisabled.value = false
          formData.value.tempEntryTranslation = formData.value.entryTranslation
          formData.value.tempExitTranslation = formData.value.exitTranslation
        } else {
          entryTranslationDisabled.value = true
        }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    
    // 
    if (formData.value.groupTranslation === 1) {
      if (!formData.value.entryTranslation) {
        message.warning('请选择进入翻译')
        return
      }
      if (!formData.value.exitTranslation) {
        message.warning('请选择发出翻译')
        return
      }
    }

    const data = formData.value as unknown as CustServiceAccountVO
    if (formType.value === 'create') {
      await CustServiceAccountApi.createCustServiceAccount(data)
      message.success(t('common.createSuccess'))
    } else {
      await CustServiceAccountApi.updateCustServiceAccount(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    account: undefined,
    password: undefined,
    managementGroup: undefined,
    ipGroup: undefined,
    entryTranslation: undefined,
    exitTranslation: undefined,
    dailyFetchLimit: undefined,
    newFriends: undefined,
    accountRegistration: undefined,
    friendTransfer: undefined,
    batchAdd: 0,
    groupTranslation: undefined,
    tempEntryTranslation: undefined,
    tempExitTranslation: undefined,
  }
  formRef.value?.resetFields()
}
const entryTranslationDisabled = ref(false)
// 开关
const handleGroupTranslationChange = (val) => {
  if (val === 0) {
    formData.value.entryTranslation = undefined
    formData.value.exitTranslation = undefined
    // 并且禁用
    entryTranslationDisabled.value = true;
  } else {
    entryTranslationDisabled.value = false;

    formData.value.entryTranslation = formData.value.tempEntryTranslation
    formData.value.exitTranslation = formData.value.tempExitTranslation
  }
}

/**
 * 发出翻译改变, 不能跟进入翻译一样
 */
const handleExitTranslationChange = () => {
  if (formData.value.entryTranslation && 
    formData.value.entryTranslation === formData.value.exitTranslation) {
      message.warning('发出翻译不能跟进入翻译一样')
      formData.value.exitTranslation = undefined
      return ;
  }
}
</script>
