<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="任务id" prop="taskId">
        <el-input v-model="formData.taskId" placeholder="请输入任务id" />
      </el-form-item>
      <el-form-item label="账号、手机号" prop="mobile">
        <el-input v-model="formData.mobile" placeholder="请输入账号、手机号" />
      </el-form-item>
      <el-form-item label="加粉人数" prop="followerGrowth">
        <el-input v-model="formData.followerGrowth" placeholder="请输入加粉人数" />
      </el-form-item>
      <el-form-item label="发起时间" prop="startTime">
        <el-date-picker
          v-model="formData.startTime"
          type="date"
          value-format="x"
          placeholder="选择发起时间"
        />
      </el-form-item>
      <el-form-item label="完成时间" prop="completionTime">
        <el-date-picker
          v-model="formData.completionTime"
          type="date"
          value-format="x"
          placeholder="选择完成时间"
        />
      </el-form-item>
      <el-form-item label="状态， 1、进行中； 2、已终止； 3、已完成；" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="成功数" prop="successNum">
        <el-input v-model="formData.successNum" placeholder="请输入成功数" />
      </el-form-item>
      <el-form-item label="等待数" prop="waitingNum">
        <el-input v-model="formData.waitingNum" placeholder="请输入等待数" />
      </el-form-item>
      <el-form-item label="失败数" prop="failNum">
        <el-input v-model="formData.failNum" placeholder="请输入失败数" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { QuickGroupSendAccountApi, QuickGroupSendAccountVO } from '@/api/wsapp/quickgroupsendaccount'
      
/** 快打群发账号汇总 表单 */
defineOptions({ name: 'QuickGroupSendAccountForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  taskId: undefined,
  mobile: undefined,
  followerGrowth: undefined,
  startTime: undefined,
  completionTime: undefined,
  status: undefined,
  successNum: undefined,
  waitingNum: undefined,
  failNum: undefined,
})
const formRules = reactive({
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await QuickGroupSendAccountApi.getQuickGroupSendAccount(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as QuickGroupSendAccountVO
    if (formType.value === 'create') {
      await QuickGroupSendAccountApi.createQuickGroupSendAccount(data)
      message.success(t('common.createSuccess'))
    } else {
      await QuickGroupSendAccountApi.updateQuickGroupSendAccount(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    taskId: undefined,
    mobile: undefined,
    followerGrowth: undefined,
    startTime: undefined,
    completionTime: undefined,
    status: undefined,
    successNum: undefined,
    waitingNum: undefined,
    failNum: undefined,
  }
  formRef.value?.resetFields()
}
</script>
