<template>
  <el-dialog
    v-model="visible"
    title="快打群发账号汇总详情"
    width="80%"
    :before-close="handleClose"
  >
    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="任务id" align="center" prop="taskId" />
      <el-table-column label="账号、手机号" align="center" prop="mobile" />
      <el-table-column label="加粉人数" align="center" prop="followerGrowth" />
      <el-table-column
        label="发起时间"
        align="center"
        prop="startTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="完成时间"
        align="center"
        prop="completionTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag
            :type="getStatusType(scope.row.status)"
            effect="plain"
          >
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="成功数" align="center" prop="successNum" />
      <el-table-column label="等待数" align="center" prop="waitingNum" />
      <el-table-column label="失败数" align="center" prop="failNum" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleView(scope.row)"
          >
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.pageNo"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 弹窗底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import { QuickGroupSendAccountApi, QuickGroupSendAccountVO } from '@/api/wsapp/quickgroupsendaccount'

// 定义组件名称
defineOptions({ name: 'QuickGroupSendAccountDialog' })

// 定义 props
const props = defineProps<{
  modelValue: boolean
  taskId?: string | number  // 可选的任务ID，用于筛选特定任务的数据
}>()

// 定义 emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const loading = ref(false)
const list = ref<QuickGroupSendAccountVO[]>([])
const total = ref(0)

// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  taskId: undefined as string | number | undefined
})

// 计算属性：控制弹窗显示
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 获取状态类型
const getStatusType = (status: number) => {
  switch (status) {
    case 1:
      return 'warning'  // 进行中
    case 2:
      return 'danger'   // 已终止
    case 3:
      return 'success'  // 已完成
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 1:
      return '进行中'
    case 2:
      return '已终止'
    case 3:
      return '已完成'
    default:
      return '未知状态'
  }
}

// 查询列表数据
const getList = async () => {
  loading.value = true
  try {
    // 如果传入了 taskId，则添加到查询参数中
    if (props.taskId) {
      queryParams.taskId = props.taskId
    }
    
    const data = await QuickGroupSendAccountApi.getQuickGroupSendAccountPage(queryParams)
    list.value = data.list
    total.value = data.total
  } catch (error) {
    console.error('获取数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size
  queryParams.pageNo = 1
  getList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  queryParams.pageNo = page
  getList()
}

// 查看详情
const handleView = (row: QuickGroupSendAccountVO) => {
  console.log('查看详情:', row)
  // 这里可以触发其他详情弹窗或跳转
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 监听弹窗打开
watch(visible, (newVal) => {
  if (newVal) {
    // 重置查询参数
    queryParams.pageNo = 1
    queryParams.pageSize = 10
    // 获取数据
    getList()
  }
})
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}
</style>
