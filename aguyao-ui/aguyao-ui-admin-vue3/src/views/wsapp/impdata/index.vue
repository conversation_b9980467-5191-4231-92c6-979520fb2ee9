<template>
  <div style="border: 1px solid #73767a;width: 49%;height:99%; float: left;">
 <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="80px"
    >
      <el-row style="margin-top: 10px;margin-left: 10px;margin-bottom: 10px;">
        <el-col :span="4">
          <el-form-item label="" prop="data">
            <el-input
              v-model="queryParams.data"
              placeholder="输入数据内容"
              clearable
              class="!w-200px"
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :offset="2" :span="10">
          <el-form-item label="" prop="createTime">
            <el-date-picker
              v-model="queryParams.createTime"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
              class="!w-240px"
            />
          </el-form-item>
        </el-col> -->
        <el-col :offset="2" :span="2">
          <el-form-item>
            <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px !w-29px" /> 搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
   <!-- 列表 -->
    <el-table
v-loading="loading" :data="list" :stripe="true"
      @selection-change="handleSelectionChange" :show-overflow-tooltip="true">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="数据id" align="center" prop="id" width="100"/>
      <el-table-column label="数据内容" align="center" prop="data" width="300"/>
      <el-table-column
        label="导入时间"
        align="center"
        prop="createTime"
        width="180px"
        :formatter="dateFormatter"
      />
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
      style="width: 50%;float: left;margin-left:10px;"
    />
  </div>
   
  <div style="border: 1px solid #73767a;width: 49%;height:99%;float: right;">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="数据" prop="" style="margin-top: 30px;">
        <div class="flex items-center" >
          <el-button type="primary" @click="handleUploadData">
            <Icon icon="ep:upload" class="mr-5px" /> 上传数据
          </el-button>
        </div>
      </el-form-item>
      <el-form-item label="" prop="data">
        <div class="mt-2 text-gray-500" style="width: 49%;">
          <el-input type="textarea" v-model="formData.data" rows="12" placeholder="一行一条数据" />
        </div>
         <div class="mt-2 text-gray-500" style="width: 49%;margin-left: 10px;">
          <el-input type="textarea" :disabled="true" v-model="formData.data2" rows="12" />
        </div>
      </el-form-item>
      <el-form-item>
        <el-button @click="submitForm" type="primary" :disabled="formLoading">提交</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ImpdataApi, ImpdataVO } from '@/api/wsapp/impdata'
import { dateFormatter } from '@/utils/formatTime'
import { get } from 'http'

/** 数据导入表单 */
defineOptions({ name: 'Impdata' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用

const loading = ref(true) // 列表的加载中
const list = ref<ImpdataVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  data:  undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ImpdataApi.getImpdataPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const selectionList = ref<ImpdataVO[]>([])

const handleSelectionChange = (val: ImpdataVO[]) => { 
  selectionList.value = val
}


const formData = ref({
  data: undefined,
  data2: undefined
})

const formRules = reactive({
    data: [{ required: true, message: '数据不能为空', trigger: 'blur' }]
})

const formRef = ref() // 表单 Ref

/** 提交表单 */
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  formData.value.data2 = ''
  try {
    const data = formData.value as unknown as ImpdataVO
    const res = await ImpdataApi.impdata(data)
    if (res.length > 0) {
      formData.value.data2 = res;
      message.warning('存在已经存在的记录，此次未插入，明细见右文本框内')
    }
    
  } finally {
    formLoading.value = false
    getList()
  }
}


/** 重置表单 */
const resetForm = () => {
  formData.value = {
    data: undefined
  }
  formRef.value?.resetFields()
}

/** 处理上传数据 */
const handleUploadData = () => {
  // 实现文件上传逻辑，并将解析后的数据填充到 formData.data
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.txt' // 只接受txt文件
  input.onchange = (event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const content = e.target?.result as string
        formData.value.data = content
      }
      reader.onerror = () => {
        message.error('文件读取失败')
      }
      reader.readAsText(file) // 以文本形式读取文件
    } else {
      message.warning('没有选择文件')
    }
  }
  input.click() // 触发文件选择对话框
}

// 页面加载时初始化
onMounted(() => {
  getList()
})
</script>

<style scoped>

</style>
