<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="账号" prop="account">
        <el-input
          v-model="queryParams.account"
          placeholder="请输入账号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      
      <!-- <el-form-item label="管理账号分组" prop="managementGroup">
        <el-input
          v-model="queryParams.managementGroup"
          placeholder="请输入管理账号分组"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="IP分组" prop="ipGroup">
        <el-input
          v-model="queryParams.ipGroup"
          placeholder="请输入IP分组"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['wsapp:cust-service-account:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>

        <el-button
          type="success"
          plain
          @click="handleBatchSetting"
          v-hasPermi="['wsapp:cust-service-account:create']"
        >
          <Icon icon="ep:setting" class="mr-5px" /> 批量设置
        </el-button>

        <el-button
          type="danger"
          plain
          @click="handleBatchDelete"
          :loading="exportLoading"
          v-hasPermi="['wsapp:cust-service-account:export']"
        >
          <Icon icon="ep:delete" class="mr-5px" /> 批量删除
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table 
      v-loading="loading" 
      :data="list" 
      :stripe="true" 
      :show-overflow-tooltip="true"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="账号" align="center" prop="account" fixed/>
      <el-table-column label="管理账号分组" align="center" prop="managementGroupStr" width="180" show-overflow-tooltip/>
      <el-table-column label="IP分组" align="center" prop="ipGroup" />
      <el-table-column label="进入翻译" align="center" prop="entryTranslation" >
        <template #default="scope">
          <dict-tag v-if="scope.row.entryTranslation" :type="DICT_TYPE.CS_ACCOUNT_TRANSLATE" :value="scope.row.entryTranslation" />
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="发出翻译" align="center" prop="exitTranslation" >
        <template #default="scope">
          <dict-tag v-if="scope.row.exitTranslation" :type="DICT_TYPE.CS_ACCOUNT_TRANSLATE" :value="scope.row.exitTranslation" />
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="单日取号" align="center" prop="dailyFetchLimit" />
      <el-table-column label="新增好友" align="center" prop="newFriends" width="120">
        <template #default="scope">
          <el-switch
            v-model="scope.row.newFriends"
            :active-value="1"
            :inactive-value="0"
            @change="handleNewFriendsChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="账号注册" align="center" prop="accountRegistration" width="180">
        <template #default="scope">
          <el-switch
            v-model="scope.row.accountRegistration"
            :active-value="1"
            :inactive-value="0"
            @change="handleAccountRegistrationChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="好友转移" align="center" prop="friendTransfer" width="120">
        <template #default="scope">
          <el-switch
            v-model="scope.row.friendTransfer"
            :active-value="1"
            :inactive-value="0"
            @change="handleFriendTransferChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="180" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click.stop="openForm('update', scope.row.id)"
            v-hasPermi="['wsapp:cust-service-account:update']"
          >
            编辑
          </el-button>

          <el-button
            link
            type="warning"
            @click.stop="openGroupForm(scope.row.id, scope.row.managementGroup)"
            v-hasPermi="['wsapp:cust-service-account:update']"
          >
            管理分组
          </el-button>

          <el-button
            link
            type="danger"
            @click.stop="handleDelete(scope.row.id)"
            v-hasPermi="['wsapp:cust-service-account:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <CustServiceAccountForm ref="formRef" @success="getList" />
  
  <!-- 分组管理弹窗 -->
  <GroupManagement ref="formGroupRef" @success="getList" />

  <!-- 批量设置弹窗 -->
  <el-dialog
    v-model="batchSettingDialogVisible"
    title="批量设置"
    width="500px"
    :close-on-click-modal="false"
  >
    <el-form :model="batchSettingForm" label-width="120px">
      <el-form-item label="群翻译">
        <el-switch
          v-model="batchSettingForm.groupTranslation"
          :active-value="1"
          :inactive-value="0"
          active-text="开启"
          inactive-text="关闭"
          @change="handleGroupTranslationChange"
        />
      </el-form-item>
      
      <el-form-item label="进入翻译">
        <el-select 
          v-model="batchSettingForm.entryTranslation" 
          placeholder="请选择"
          clearable
          style="width: 100%"
          :disabled="entryTranslationDisabled"
        >
          <el-option label="请选择" value="" />
          <el-option 
            v-for="dict in getStrDictOptions(DICT_TYPE.CS_ACCOUNT_TRANSLATE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="发出翻译">
        <el-select 
          v-model="batchSettingForm.exitTranslation" 
          placeholder="请选择"
          clearable
          style="width: 100%"
          :disabled="entryTranslationDisabled"
          @change="handleExitTranslationChange"
        >
          <el-option label="请选择" value="" />
          <el-option 
            v-for="dict in getStrDictOptions(DICT_TYPE.CS_ACCOUNT_TRANSLATE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="单日取号">
        <el-input 
          v-model="batchSettingForm.dailyFetchLimit" 
          placeholder="取号数量（0为不限制，留空不进行此项设置）"
          type="number"
        />
        <div style="font-size: 12px; color: #999; margin-top: 4px;">
          单日取号数量（0为不限制，留空不进行此项设置）
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="batchSettingDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleBatchSettingConfirm">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import CustServiceAccountForm from './CustServiceAccountForm.vue'
import GroupManagement from './GroupManagement.vue'
import { CustServiceAccountApi, CustServiceAccountVO } from '@/api/wsapp/custserviceaccount'


/** 客服账号信息表 列表 */
defineOptions({ name: 'CustServiceAccount' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<CustServiceAccountVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const selectedRows = ref<CustServiceAccountVO[]>([]) // 选中的行
const batchSettingDialogVisible = ref(false) // 批量设置弹窗显示状态
const batchSettingForm = reactive({
  entryTranslation: undefined,
  exitTranslation: undefined,
  groupTranslation: 0,
  dailyFetchLimit: 0
})
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  account: undefined,
  password: undefined,
  managementGroup: undefined,
  ipGroup: undefined,
  entryTranslation: undefined,
  exitTranslation: undefined,
  dailyFetchLimit: undefined,
  newFriends: undefined,
  accountRegistration: undefined,
  createTime: [],
  friendTransfer: undefined,
  batchAdd: undefined,
  groupTranslation: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CustServiceAccountApi.getCustServiceAccountPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 管理分组操作 */
const formGroupRef = ref() 
const openGroupForm = (id?: number, managementGroup: string) => {
  formGroupRef.value.open(id, managementGroup)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await CustServiceAccountApi.deleteCustServiceAccount(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 选中行变化 */
const handleSelectionChange = (selection: CustServiceAccountVO[]) => {
  selectedRows.value = selection
}

/** 批量删除 */
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    message.warning('请先选择要删除的数据')
    return
  }
  try {
    // 删除的二次确认
    await message.delConfirm(`确定要删除选中的 ${selectedRows.value.length} 条数据吗？`)
    // 提取选中行的ID
    const ids = selectedRows.value.map(row => row.id)
    // 发起批量删除
    await CustServiceAccountApi.deleteCustServiceAccountBatch(ids)
    message.success('批量删除成功')
    // 刷新列表
    await getList()
    // 清空选中
    selectedRows.value = []
  } catch {}
}

/** 批量设置 */
const handleBatchSetting = async () => {
  if (selectedRows.value.length === 0) {
    message.warning('请先选择要设置的数据')
    return
  }
  // 重置表单
  Object.assign(batchSettingForm, {
    entryTranslation: undefined,
    exitTranslation: undefined,
    groupTranslation: 0,
    dailyFetchLimit: 0
  })
  batchSettingDialogVisible.value = true
}

/** 批量设置确认 */
const handleBatchSettingConfirm = async () => {
  try {
    // 提取选中行的ID
    const ids = selectedRows.value.map(row => row.id)
    
    // 构建批量更新的数据
    const updateData: any = {
      ids: ids
    }

    if (batchSettingForm.groupTranslation === 1) {
      if (!batchSettingForm.entryTranslation) {
        message.warning('请选择进入翻译')
        return
      }
      if (!batchSettingForm.exitTranslation) {
        message.warning('请选择发出翻译')
        return
      }
    }
    
    // 只添加有值的字段
    if (batchSettingForm.entryTranslation !== undefined) {
      updateData.entryTranslation = batchSettingForm.entryTranslation
    }
    if (batchSettingForm.exitTranslation !== undefined) {
      updateData.exitTranslation = batchSettingForm.exitTranslation
    }
    if (batchSettingForm.groupTranslation !== undefined) {
      updateData.groupTranslation = batchSettingForm.groupTranslation
    }
    if (batchSettingForm.dailyFetchLimit !== undefined) {
      updateData.dailyFetchLimit = batchSettingForm.dailyFetchLimit
    }
    
    console.log(updateData);
    // 发起批量更新
    await CustServiceAccountApi.updateCustServiceAccountBatch(updateData)
    message.success('批量设置成功')
    
    // 关闭弹窗
    batchSettingDialogVisible.value = false
    
    // 刷新列表
    await getList()
    
    // 清空选中
    selectedRows.value = []
  } catch {
    message.error('批量设置失败')
  }
}

/** 新增好友开关变化 */
const handleNewFriendsChange = async (row: CustServiceAccountVO) => {
  try {
    await CustServiceAccountApi.updateCustServiceAccountSwitch({
      id: row.id,
      type: 1,
      switchBtn: row.newFriends
    })
    message.success('新增好友设置更新成功')
  } catch {
    // 如果更新失败，恢复原值
    row.newFriends = row.newFriends === 1 ? 0 : 1
  }
}

/** 账号注册开关变化 */
const handleAccountRegistrationChange = async (row: CustServiceAccountVO) => {
  try {
    await CustServiceAccountApi.updateCustServiceAccountSwitch({
      id: row.id,
      type: 2,
      switchBtn: row.accountRegistration
    })
    message.success('账号注册设置更新成功')
  } catch {
    // 如果更新失败，恢复原值
    row.accountRegistration = row.accountRegistration === 1 ? 0 : 1
  }
}

/** 好友转移开关变化 */
const handleFriendTransferChange = async (row: CustServiceAccountVO) => {
  try {
    await CustServiceAccountApi.updateCustServiceAccountSwitch({
      id: row.id,
      type: 3,
      switchBtn: row.friendTransfer
    })
    message.success('好友转移设置更新成功')
  } catch {
    // 如果更新失败，恢复原值
    row.friendTransfer = row.friendTransfer === 1 ? 0 : 1
  }
}

const entryTranslationDisabled = ref(true)
// 开关
const handleGroupTranslationChange = (val) => {
  if (val === 0) {
    batchSettingForm.entryTranslation = undefined
    batchSettingForm.exitTranslation = undefined
    // 并且禁用
    entryTranslationDisabled.value = true;
  } else {
    entryTranslationDisabled.value = false;
  }
}
/**
 * 发出翻译改变, 不能跟进入翻译一样
 */
const handleExitTranslationChange = () => {
  if (batchSettingForm.entryTranslation && 
    batchSettingForm.entryTranslation === batchSettingForm.exitTranslation) {
      message.warning('发出翻译不能跟进入翻译一样')
      batchSettingForm.exitTranslation = undefined
      return ;
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
