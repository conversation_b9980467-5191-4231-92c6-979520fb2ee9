<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="600px">
    <div v-loading="loading">
      <!-- 已分配的分组 -->
      <div class="group-section">
        <h4 class="section-title">该客服已管理分组：</h4>
        <div class="group-list assigned-groups">
          <div 
            v-for="group in assignedGroups" 
            :key="group.id"
            class="group-item assigned"
            @click="removeFromAssigned(group)"
          >
            <el-icon class="check-icon"><Check /></el-icon>
            {{ group.groupName }}
          </div>
          <div v-if="assignedGroups.length === 0" class="empty-text">
            暂无分配的分组
          </div>
        </div>
      </div>

      <!-- 未分配的分组 -->
      <div class="group-section">
        <h4 class="section-title">该客服未管理分组：</h4>
        <div class="group-list unassigned-groups">
          <div 
            v-for="group in unassignedGroups" 
            :key="group.id"
            class="group-item unassigned"
            @click="addToAssigned(group)"
          >
            {{ group.groupName }}
          </div>
          <div v-if="unassignedGroups.length === 0" class="empty-text">
            暂无可分配的分组
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">立即提交</el-button>
        <el-button @click="handleReset">重置</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { Check } from '@element-plus/icons-vue'
import { GroupApi, GroupVO } from '@/api/wsapp/group'
import { CustServiceAccountApi } from '@/api/wsapp/custserviceaccount'

/** 分组管理组件 */
defineOptions({ name: 'GroupManagement' })

const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗显示状态
const dialogTitle = ref('管理分组') // 弹窗标题
const loading = ref(false) // 加载状态
const submitting = ref(false) // 提交状态
const accountId = ref<number>() // 当前账号ID
const managementGroupIds = ref<string>() // 客服管理分组id列表

// 分组数据
const allGroups = ref<GroupVO[]>([]) // 所有分组
const assignedGroups = ref<GroupVO[]>([]) // 已分配的分组
const unassignedGroups = ref<GroupVO[]>([]) // 未分配的分组
const originalAssignedGroups = ref<GroupVO[]>([]) // 原始已分配分组（用于重置）

/** 打开弹窗 */
const open = async (id: number, managementGroup: string) => {
  accountId.value = id
  dialogVisible.value = true
  managementGroupIds.value = managementGroup
  await loadData()
}

/** 加载数据 */
const loadData = async () => {
  loading.value = true
  try {
    // 获取所有分组
    const groupsData = await GroupApi.getGroupList()
    allGroups.value = groupsData || []
    
    // 获取账号已分配的分组
    if (managementGroupIds !== null) {
      const accountGroupsData = managementGroupIds.value?.split(',') || []
      const assignedGroupIds = accountGroupsData?.map((item: any) => parseInt(item)) || []

      // 分离已分配和未分配的分组
      assignedGroups.value = allGroups.value.filter(group => assignedGroupIds.includes(group.id))
      unassignedGroups.value = allGroups.value.filter(group => !assignedGroupIds.includes(group.id))
      
      // 保存原始已分配分组（用于重置）
      originalAssignedGroups.value = [...assignedGroups.value]
    }
    
  } catch (error) {
    console.error('加载分组数据失败:', error)
    message.error('加载分组数据失败')
  } finally {
    loading.value = false
  }
}

/** 从已分配中移除分组 */
const removeFromAssigned = (group: GroupVO) => {
  const index = assignedGroups.value.findIndex(g => g.id === group.id)
  if (index > -1) {
    assignedGroups.value.splice(index, 1)
    unassignedGroups.value.push(group)
    // 按ID排序
    unassignedGroups.value.sort((a, b) => a.id - b.id)
  }
}

/** 添加到已分配 */
const addToAssigned = (group: GroupVO) => {
  const index = unassignedGroups.value.findIndex(g => g.id === group.id)
  if (index > -1) {
    unassignedGroups.value.splice(index, 1)
    assignedGroups.value.push(group)
    // 按ID排序
    assignedGroups.value.sort((a, b) => a.id - b.id)
  }
}

/** 重置分组 */
const handleReset = () => {
  assignedGroups.value = [...originalAssignedGroups.value]
  unassignedGroups.value = allGroups.value.filter(
    group => !originalAssignedGroups.value.some(assigned => assigned.id === group.id)
  )
  unassignedGroups.value.sort((a, b) => a.id - b.id)
}

/** 提交分组设置 */
const emit = defineEmits(['success'])
const handleSubmit = async () => {
  submitting.value = true
  try {
    // 提取已分配分组的ID
    const groupIds = assignedGroups.value.map(group => group.id)
    debugger
    console.log(groupIds)
    // 为每个分组调用批量设置账号信息API
    // 确保 accountId 不为 undefined 再调用 API
    if (accountId.value === undefined) {
      throw new Error('账号ID不能为空')
    }
    await CustServiceAccountApi.setCustServiceAccountGroup(accountId.value, groupIds)
    
    message.success('分组设置成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('设置分组失败:', error)
    message.error('设置分组失败')
  } finally {
    submitting.value = false
  }
}

// 暴露open方法
defineExpose({ open })
</script>

<style scoped>
.group-section {
  margin-bottom: 20px;
}

.section-title {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.group-list {
  min-height: 120px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background-color: #fafafa;
}

.assigned-groups {
  background-color: #f0f9ff;
  border-color: #409eff;
}

.group-item {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  margin: 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 13px;
  user-select: none;
}

.group-item.assigned {
  background-color: #409eff;
  color: white;
  border: 1px solid #409eff;
}

.group-item.assigned:hover {
  background-color: #f56c6c;
  border-color: #f56c6c;
}

.group-item.unassigned {
  background-color: white;
  color: #606266;
  border: 1px solid #dcdfe6;
}

.group-item.unassigned:hover {
  background-color: #409eff;
  color: white;
  border-color: #409eff;
}

.check-icon {
  margin-right: 4px;
  font-size: 12px;
}

.empty-text {
  text-align: center;
  color: #909399;
  font-size: 13px;
  padding: 20px;
}

.dialog-footer {
  text-align: center;
}
</style>
