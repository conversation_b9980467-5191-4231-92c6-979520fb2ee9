<template>
  <Dialog v-model="dialogVisible" title="发送新增任务" width="800px" v-loading="submitLoading">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <!-- 标签名选择 -->
      <el-form-item label="标签名" prop="tagId">
        <el-select v-model="formData.tagId" placeholder="请选择标签" clearable class="mr-2" style="width: 100%;">
          <el-option
            v-for="item in tagList"
            :key="item.id"
            :label="item.tagName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <!-- 分组选择 -->
      <el-form-item label="选择分组" prop="groupId">
       <el-select
          v-model="formData.groupId" placeholder="请选择分组" 
          clearable class="mr-2" style="width: 100%;"
          @change="getAcctList">
          <el-option
            v-for="item in groupList"
            :key="item.id"
            :label="item.groupName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <!-- 选择账号 -->
      <el-form-item label="选择账号" prop="mobile">
        <el-select
          v-model="formData.mobile"
          multiple
          placeholder="请选择账号" 
          clearable class="mr-2" style="width: 100%;">
          <el-option
            v-for="item in acctList"
            :key="item.mobile"
            :label="item.mobile"
            :value="item.mobile"
          />
        </el-select>
      </el-form-item>

      <!-- 任务名称 -->
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="formData.taskName"
          placeholder="请输入任务名称"
          style="width: 100%"
        />
      </el-form-item>


      
      <!-- 通道类型 -->
      <el-form-item label="通道类型" prop="channelType">
        <el-radio-group v-model="formData.channelType">
          <el-radio :label="0">通讯录</el-radio>
          <el-radio :label="1">链接</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 添加资源 -->
      <el-form-item label="添加资源" prop="resourceType">
        <el-radio-group v-model="formData.resourceType">
          <el-radio label="upload">上传</el-radio>
          <el-radio label="library">资源库</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 文件上传 -->
      <el-form-item v-if="formData.resourceType === 'upload'" label="" prop="fileContent">
        <!-- <div style="width: 100%"> -->
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :show-file-list="false"
            accept=".txt"
            :on-change="handleFileChange"
            style="margin-bottom: 10px"
          >
            <el-button type="success" :icon="Upload">上传TXT文件</el-button>
          </el-upload>
          <!-- <div class="upload-tip">手机号，一行一个资源，以回车生成，合行不得超过150</div>
          <el-input
            v-model="formData.fileContent"
            type="textarea"
            :rows="8"
            placeholder="文件内容将显示在这里..."
            style="width: 100%; margin-top: 10px"
          /> -->
        <!-- </div> -->
      </el-form-item>

      <el-form-item label="" prop="fileContent">
        <!-- <div style="width: 100%"> -->
          <div class="upload-tip">手机号，一行一个资源，以回车生成，合行不得超过2000</div>
          <el-input
            v-model="formData.fileContent"
            type="textarea"
            :rows="8"
            placeholder="文件内容将显示在这里..."
            style="width: 100%; margin-top: 10px"
          />
        <!-- </div> -->
      </el-form-item>


      <!-- 拔打电话 -->
      <el-form-item label="拔打电话" prop="callPhone">
        <el-radio-group v-model="formData.callPhone">
          <el-radio :label="0">关闭</el-radio>
          <el-radio :label="1">开启</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 拔打视频 -->
      <el-form-item label="拔打视频" prop="callVideo">
        <el-radio-group v-model="formData.callVideo">
          <el-radio :label="0">关闭</el-radio>
          <el-radio :label="1">开启</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 拔打时长 -->
      <el-form-item label="拔打时长" prop="callDuration">
        <el-radio-group v-model="formData.callDuration">
          <el-radio label="default">默认(1~3s)</el-radio>
          <el-radio label="long">20~25s</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 群发黑科技 -->
      <!-- <el-form-item label="群发黑科技" prop="blackTech">
        <el-radio-group v-model="formData.blackTech">
          <el-radio label="open" :value="1">开启</el-radio>
          <el-radio label="close" :value="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item> -->

      <!-- 发送内容 -->
      <el-form-item label="发送内容" prop="sendContent">
        <div style="width: 100%">
          <div class="send-buttons" style="margin-bottom: 10px">
            <el-button type="success" size="small" :icon="Edit" @click="showTextDialog">添加文字</el-button>
            <el-button type="primary" size="small" :icon="Picture" @click="showImageDialog">添加图片</el-button>
            <!-- <el-button type="warning" size="small" :icon="Link" @click="showGroupLinkDialog">群组链接</el-button> -->
            <el-button type="info" size="small" :icon="Microphone" @click="showVoiceDialog">添加语音</el-button>
            <el-button type="danger" size="small" :icon="VideoCamera" @click="showCardDialog">添加名片</el-button>
            <el-button type="primary" size="small" :icon="Picture" @click="showImgTextDialog">添加图文</el-button>
            <el-button type="warning" size="small" :icon="Link" @click="showButtonDialog">转发超链</el-button>
          </div>
        </div>

        <!-- 内容预览区域 -->
        <div class="content-preview" v-if="contentItems.length > 0">
          <div class="content-item" v-for="(item, index) in contentItems" :key="index">
            <div class="content-header">
              <span class="content-type">{{ getContentTypeLabel(item.type) }}</span>
              <el-button type="danger" size="small" text @click="removeContentItem(index)">删除</el-button>
            </div>
            <div class="content-body">
              <!-- 文本内容 -->
              <div v-if="item.type === 'text'" class="text-content">
                {{ item.content }}
              </div>
              <!-- 图片内容 -->
              <div v-if="item.type === 'image'" class="image-content">
                <img :src="item.content" alt="图片" style="max-width: 200px; max-height: 150px;" />
              </div>
              <!-- 群组链接内容 -->
              <div v-if="item.type === 'groupLink'" class="group-link-content">
                <div class="group-info">
                  <img v-if="item.avatar" :src="item.avatar" alt="群头像" class="group-avatar" />
                  <div class="group-details">
                    <div class="group-name">{{ item.groupName }}</div>
                    <div class="group-link">{{ item.link }}</div>
                  </div>
                </div>
              </div>
              <!-- 语音内容 -->
              <div v-if="item.type === 'voice'" class="voice-content">
                <audio :src="item.content" controls style="width: 300px;"></audio>
              </div>
              <!-- 名片内容 -->
              <div v-if="item.type === 'card'" class="card-content">
                {{ item.content }}
              </div>
               <!-- 图文内容 -->
              <div v-if="item.type === 'imgText'" class="imgText-content">
                <div class="display-area">
                    <!-- 标题展示 -->
                    <h4 class="item-title" >{{ item.contentObj.title || '无标题' }}</h4>
                    
                    <!-- 背景图片 -->
                    <div v-if="item.contentObj.imageUrl" class="item-image">
                      <img :src="item.contentObj.imageUrl" alt="背景图" />
                    </div>
                    
                    <!-- 内容展示 -->
                    <div class="item-body" v-if="item.contentObj.body">
                      <p>{{ item.contentObj.body }}</p>
                    </div>
                    
                    
                    <!-- 页脚展示 -->
                    <div class="item-footer" v-if="item.contentObj.footer">
                      <p>{{ item.contentObj.footer }}</p>
                    </div>

                    <!-- 按钮展示 -->
                    <div class="item-buttons" v-if="item.contentObj.buttons && item.contentObj.buttons.length">
                      <el-button 
                        v-for="(btn, btnIndex) in item.contentObj.buttons" 
                        :key="btnIndex"
                        type="primary"
                        class="block-button"
                      >
                        {{ btn.text || '按钮' }}
                      </el-button>
                    </div>
                </div>
              </div>
               <!-- 转发超链内容 -->
              <div v-if="item.type === 'button'" class="button-content">
                <div class="display-area">
                    <!-- 内容展示 -->
                    <div class="item-body" v-if="item.contentObj.text">
                      <p>{{ item.contentObj.text }}</p>
                    </div>
                    <div class="item-body" v-if="item.contentObj.businessId">
                      <p>{{ item.contentObj.businessId }}</p>
                    </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-form-item>


      <!-- 单账号新增数量 -->
      <el-form-item label="单账号新增数量" prop="singleAccountNewCount" label-width="128px">
        <el-input-number
          v-model="formData.singleAccountNewCount"
          :min="1"
          :max="1000"
          style="width: 200px"
        />
      </el-form-item>

      
      <!-- 间隔时间 -->
      <el-form-item label="间隔时间" prop="intervalTime">
        <div style="display: flex; align-items: center; gap: 12px;">
          <el-input-number
            v-model="formData.intervalTimeStart"
            :min="1"
            :max="1000"
            placeholder="开始"
            style="width: 120px" />
          <span>~</span>
          <el-input-number
            v-model="formData.intervalTimeEnd"
            :min="formData.intervalTimeStart"
            :max="1000"
            placeholder="结束"
            style="width: 120px" />
          <span>分钟</span>
        </div>
      </el-form-item>

      <!-- 连续新增几次进入普通风控 -->
      <el-form-item label="连续新增几次进入普通风控" prop="timesToRiskControl" label-width="188px">
        <el-input-number
          v-model="formData.timesToRiskControl"
          :min="1"
          :max="1000"
          style="width: 200px"
        />
      </el-form-item>

      <!-- 普通风控间隔时间（分钟） -->
      <el-form-item label="普通风控间隔时间（分钟）" prop="riskControlInterval" label-width="188px">
        <el-input-number
          v-model="formData.riskControlInterval"
          :min="1"
          :max="1000"
          style="width: 200px"
        />
      </el-form-item>

      <!-- 开始时间 -->
      <el-form-item label="开始时间" prop="startTime">
        <!-- <el-date-picker
          v-model="formData.startTime"
          type="datetime"
          placeholder="选择开始时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 240px"
        /> -->
        <el-date-picker
          v-model="formData.startTime"
          type="datetime"
          value-format="x"
          placeholder="选择开始时间"
          class="!w-240px"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">立即发起任务</el-button>
      </div>
    </template>
  </Dialog>

  <!-- 添加文字弹窗 -->
  <Dialog v-model="textDialogVisible" title="添加文字" width="500px">
    <el-input
      v-model="textContent"
      type="textarea"
      :rows="6"
      placeholder="请输入文字内容"
      style="width: 100%"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="textDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addTextContent">确定</el-button>
      </div>
    </template>
  </Dialog>

  <!-- 添加图片弹窗 -->
  <Dialog v-model="imageDialogVisible" title="添加图片" width="500px">
      <el-upload
      ref="imageUploadRef"
      :auto-upload="false"
      :show-file-list="false"
      accept="image/*"
      :on-change="handleImageChange"
      style="margin-bottom: 10px"
    >
      <el-button type="primary" :icon="Upload">选择图片</el-button>
    </el-upload>

    <!-- 图片预览区域 -->
    <div v-if="imagePreview" class="image-preview">
      <img :src="imagePreview" alt="预览" style="max-width: 100%; max-height: 300px;" />
    </div>

    <template #footer>
      <div class="dialog-footer">
          <el-button @click="closeImgDialog">取消</el-button>
          <el-button type="primary" @click="uploadImgToOss(1)" :disabled="uploading">{{ uploading ? '上传中...' : '确定' }}</el-button>
      </div>
    </template>
    <!-- <el-upload
      ref="imageUploadRef"
      :auto-upload="false"
      :show-file-list="false"
      accept="image/*"
      :on-change="handleImageChange"
      style="margin-bottom: 10px"
    >
    <el-button type="primary" :icon="Upload">选择图片</el-button>
    </el-upload>
    <div v-if="imagePreview" class="image-preview">
      <img :src="imagePreview" alt="预览" style="max-width: 100%; max-height: 300px;" />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="imageDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addImageContent" :disabled="!imagePreview">确定</el-button>
      </div>
    </template> -->
  </Dialog>

  <!-- 群组链接弹窗 -->
  <Dialog v-model="groupLinkDialogVisible" title="群组链接" width="600px">
    <el-form :model="groupLinkForm" label-width="80px">
      <el-form-item label="群组文字">
        <el-input v-model="groupLinkForm.groupText" placeholder="请输入群组文字信息（此处不要填写群链接，会自动补充）" />
      </el-form-item>
      <el-form-item label="群链接">
        <div style="display: flex; gap: 10px; align-items: center;">
          <el-input v-model="groupLinkForm.link" placeholder="请输入群组链接" style="flex: 1;" />
          <el-button type="primary" @click="parseGroupLink">解析</el-button>
        </div>
      </el-form-item>
      <el-form-item label="群头像" v-if="groupLinkForm.avatar">
        <img :src="groupLinkForm.avatar" alt="群头像" style="width: 50px; height: 50px; border-radius: 4px;" />
      </el-form-item>
      <el-form-item label="群名称" v-if="groupLinkForm.groupName">
        <span>{{ groupLinkForm.groupName }}</span>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="groupLinkDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addGroupLinkContent" :disabled="!groupLinkForm.link">确定</el-button>
      </div>
    </template>
  </Dialog>

  <!-- 添加语音弹窗 -->
  <Dialog v-model="voiceDialogVisible" title="添加语音" width="500px">
    <el-upload
      ref="voiceUploadRef"
      :auto-upload="false"
      :show-file-list="false"
      accept="audio/*"
      :on-change="handleVoiceChange"
      style="margin-bottom: 10px"
    >
      <el-button type="primary" :icon="Upload">选择语音文件</el-button>
      <div style="background-color:#fdf6ec;color:#e6a23c;margin-top: 5px;">请上传mp3格式文件</div>
    </el-upload>
    <div v-if="voicePreview" class="voice-preview">
      <audio :src="voicePreview" controls style="width: 100%;"></audio>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeVoiceDialog">取消</el-button>
        <el-button type="primary" @click="uploadVoiceToOss" :disabled="uploading">确定</el-button>
      </div>
    </template>
  </Dialog>

  <!-- 添加名片弹窗 -->
  <Dialog v-model="cardDialogVisible" title="添加名片" width="500px">
    <el-input
      v-model="cardContent"
      type="textarea"
      :rows="10"
      placeholder="名片信息(格式:名称#手机号,一行一个名片,多个名片随机发送)"
      style="width: 100%"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cardDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addCardContent">确定</el-button>
      </div>
    </template>
  </Dialog>

  <!-- 添加图文弹窗 -->
  <Dialog v-model="imgTextDialogVisible" title="添加图文" width="600px">
    <el-form ref="imgTextFormRef" :model="imgTextForm" label-width="80px" :rules="imgTextRules">
      <el-form-item label="页面标题" prop="title">
        <el-input v-model="imgTextForm.title" placeholder="请输入页面标题" />
      </el-form-item>
      <el-form-item label="页面内容" prop="body">
          <el-input v-model="imgTextForm.body" placeholder="请输入页面内容" type="textarea" :rows="5"/>
      </el-form-item>
      <el-form-item label="页脚内容" prop="footer">
          <el-input v-model="imgTextForm.footer" placeholder="请输入页脚内容"/>
      </el-form-item>
       <!-- 动态按钮区域 -->
      <el-form-item label="按钮设置" prop="buttons">
        <div class="button-group">
          <!-- 按钮列表 -->
          <div v-for="(btn, index) in imgTextForm.buttons" :key="index" class="button-item">
            <el-input 
              v-model="btn.text" 
              placeholder="按钮文字" 
              class="button-input"
            />
            <el-input 
              v-model="btn.url" 
              placeholder="跳转链接" 
              class="button-input"
            />
            <el-button 
              type="danger" 
              @click="removeButton(index)"
              size="small">删除</el-button>
          </div>
          
          <!-- 添加按钮 -->
          <el-button 
            type="primary" 
            icon="Plus" 
            @click="addButton"
            style="margin-top: 10px"
          >
            添加按钮
          </el-button>
        </div>
      </el-form-item>
      <el-form-item label="背景图片">
           <el-upload
            ref="imageTextUploadRef"
            :auto-upload="false"
            :show-file-list="false"
            accept="image/*"
            :on-change="handleImageChange"
            style="margin-bottom: 10px"
          >
            <el-button type="primary" :icon="Upload">选择图片</el-button>
          </el-upload>
          <!-- 图片预览区域 -->
          <div v-if="imagePreview" class="image-preview">
            <img :src="imagePreview" alt="预览" style="max-width: 100%; max-height: 300px;" />
          </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeImgTextDialog">取消</el-button>
        <el-button type="primary" @click="uploadImgToOss(2)" :disabled="uploading">{{ uploading ? '上传中...' : '确定' }}</el-button>
      </div>
    </template>
  </Dialog>
  <!-- 添加转发超链消息弹窗 -->
  <Dialog v-model="buttonDialogVisible" title="添加转发超链消息" width="500px">
    <el-form ref="buttonFormRef" :model="buttonForm" label-width="100px" :rules="buttonRules">
      <el-form-item label="消息内容" prop="text">
        <el-input v-model="buttonForm.text" placeholder="请输入页面标题" type="textarea" :rows="5"/>
      </el-form-item>
      <el-form-item label="转发WS账号" prop="businessId">
          <el-input v-model="buttonForm.businessId" placeholder="请输入转发WS账号（一行一个号码，随机选取一个发送）" type="textarea" :rows="5"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="buttonDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addButtonContent">确定</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { QuickGroupSendApi, MessageVO  } from '@/api/wsapp/quickgroupsend'
import { Upload, Edit, Picture, Link, Microphone, VideoCamera } from '@element-plus/icons-vue'
import { TagApi, TagVO } from '@/api/wsapp/tag'
import { GroupApi, GroupVO } from '@/api/wsapp/group'
import { AccountApi, AccountVO } from '@/api/wsapp/account'
import { OssApi } from '@/api/wsapp/oss'

defineOptions({ name: 'SendTaskDialog' })

const dialogVisible = ref(false)
// 遮罩
const submitLoading = ref(false)
const formRef = ref()
const imgTextFormRef = ref()
const buttonFormRef = ref()

const uploadRef = ref()
const imageTextUploadRef = ref()
const imageUploadRef = ref()
const voiceUploadRef = ref()

// 内容相关的弹窗状态
const textDialogVisible = ref(false)
const imageDialogVisible = ref(false)
const groupLinkDialogVisible = ref(false)
const voiceDialogVisible = ref(false)
const cardDialogVisible = ref(false)
const imgTextDialogVisible = ref(false)
const buttonDialogVisible = ref(false)

// 内容相关的数据
const textContent = ref('')
const imagePreview = ref('')
const selectedFile = ref(null)
const selectedVoice = ref(null)
const uploading = ref(false)
const uploadProgress = ref(0)

const voicePreview = ref('')
const cardContent = ref('')
const imgTextContent = ref('')
const buttonContent = ref('')

// interface ContentItem {
//   type: string
//   [key: string]: any
// }
const contentItems = ref<MessageVO[]>([])

const tagList = ref<TagVO[]>([]) // 标签下拉框数据
const groupList = ref<GroupVO[]>([]) // 分组下拉框数据
const acctList = ref<AccountVO[]>([]) // 账号下拉框数据

/** 查询标签列表 */
const getTagList = async () => {
  try {
    const data = await TagApi.getTagList()
    tagList.value = data
  } finally {
  }
}

/** 查询分组列表 */
const getGroupList = async () => {
  try {
    const data = await GroupApi.getGroupList()
    groupList.value = data
  } finally {
  }
}

/** 查询账号列表 */
const getAcctList = async () => {
  try {
    const data = await AccountApi.getAcctList({ groupOwned: formData.groupId })
    acctList.value = data
  } finally {
  }
}

// 群组链接表单
const groupLinkForm = reactive({
  groupText: '',
  // link: '',
  avatar: '',
  content: '',
  link: '',
  groupName: ''
})

// 图文消息表单
const imgTextForm = reactive({
  title: '',
  body: '',
  footer: '',
  imageUrl: '',
  buttons: []
})

// 超链消息表单
const buttonForm = reactive({
  text: '',
  businessId: ''
})

// 表单数据
const formData = reactive({
  id: 0,
  unallocated: '', 
  repetitionNum: 0, 
  taskTotal: 0, 
  execNum: 0,
  successNum: 0, 
  failNum: 0, 
  pendingNum: 0, 
  replyNum: 0,
  replyRate: 0, 
  progress: 0, 
  interval: 0,


  tagId: '',
  groupId: '',
  mobile: [],
  taskName: '',
  intervalTime: 10,
  resourceType: 'upload',
  fileContent: '',
  blackTech: 0,
  sendContent: '',
  singleAccountNewCount: 1,
  startTime: new Date,
  messageContents: contentItems,

  channelType: 0,     // 通道类型，0-通讯录，1-链接
  callPhone: 0,      // 拔打电话，0-关闭，1-开启
  callVideo: 0,      // 拔打视频，0-关闭，1
  callDuration: 'default', // 拔打时长，默认1~3s，20~25s
  intervalTimeStart: 1, // 间隔时间开始
  intervalTimeEnd: 3, // 间隔时间结束
  timesToRiskControl: undefined, // 连续新增几次进入普通风控
  riskControlInterval: undefined, // 普通风控间隔时间（分钟）
})

// 表单验证规则
const rules = {
  tagId: [
    { required: true, message: '请选择标签', trigger: 'blur' }
  ],
  groupId: [
    { required: true, message: '请选择分组', trigger: 'blur' }
  ],
  mobile: [
    { required: true, message: '请选择账号', trigger: 'blur' }
  ],
  taskName: [
    { required: true, message: '请输入任务名称', trigger: 'blur' }
  ],
  intervalTime: [
    { required: true, message: '请输入间隔时间', trigger: 'blur' }
  ],
  singleAccountNewCount: [
    { required: true, message: '请输入单账号新增数量', trigger: 'blur' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  fileContent: [
    { required: true, message: '请输入文件内容', trigger: 'change' }
  ],
  intervalTimeEnd: [
    { required: true, message: '请输入间隔时间结束', trigger: 'blur' }
  ]
}
const imgTextRules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' }
  ],
  body: [
    { required: true, message: '请输入页面内容', trigger: 'blur' }
  ],
  footer: [
    { required: true, message: '请输入页脚内容', trigger: 'blur' }
  ],
    buttons: [
    { 
      validator: (rule, value, callback) => {
        // 验证逻辑：数组必须存在且至少有一个元素
        if (!Array.isArray(value) || value.length === 0) {
          callback(new Error('至少需要添加一个按钮'));
        } else {
          // 可以进一步验证每个按钮的文字和链接是否填写
          const hasInvalidBtn = value.some(btn => !btn.text || !btn.url);
          if (hasInvalidBtn) {
            callback(new Error('请完善所有按钮的文字和链接'));
          } else {
            callback(); // 验证通过
          }
        }
      },
      trigger: ['blur', 'change'] // 失焦或数组变化时触发验证
    }
  ]
}
const buttonRules = {
  text: [
    { required: true, message: '请输入消息内容', trigger: 'blur' }
  ],
  businessId: [
    { required: true, message: '请输入转发WS账号', trigger: 'blur' }
  ]
}
// 文件上传处理
const handleFileChange = (file: any) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    const content = e.target?.result as string
    // 解析文件内容到文本框
    formData.fileContent = content
    ElMessage.success('文件上传成功')
  }
  reader.readAsText(file.raw)
}

// 提交表单
const emit = defineEmits(['success']) 
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitLoading.value = true
    
    // 验证文件内容行数
    if (formData.resourceType === 'upload' && formData.fileContent) {
      const lines = formData.fileContent.split('\n').filter(line => line.trim())
      if (lines.length > 2000) {
        ElMessage.error('文件内容不能超过2000行')
        return
      }
    }
    
    // console.log('提交数据:', formData)
    await QuickGroupSendApi.createQuickGroupSend(formData)
    
    ElMessage.success('任务创建成功')
    dialogVisible.value = false
  } finally  {
    submitLoading.value = false
    emit('success')
  }
}


// 显示各种内容弹窗的方法
const showTextDialog = () => {
  textContent.value = ''
  textDialogVisible.value = true
}

const showImageDialog = () => {
  imagePreview.value = ''
  imageDialogVisible.value = true
}

const showGroupLinkDialog = () => {
  Object.assign(groupLinkForm, {
    groupText: '',
    // link: '',
    avatar: '',
    content: ''
  })
  groupLinkDialogVisible.value = true
}

const showVoiceDialog = () => {
  voicePreview.value = ''
  voiceDialogVisible.value = true
}

const showCardDialog = () => {
  cardContent.value = ''
  cardDialogVisible.value = true
}

const showImgTextDialog = () => {
   // 手动重置所有字段
  Object.assign(imgTextForm, {
    title: '',
    body: '',
    footer: '',
    imageUrl: '',
    buttons: []
  })
  imagePreview.value = ''
  imgTextDialogVisible.value = true
}

const showButtonDialog = () => {
  // 手动重置所有字段
  Object.assign(buttonForm, {
    text: '',
    businessId: ''
  })
  buttonDialogVisible.value = true
}
// 添加按钮
const addButton = () => {
  imgTextForm.buttons.push({
    text: '',
    url: ''
  });
};

// 删除按钮
const removeButton = (index) => {
  imgTextForm.buttons.splice(index, 1);
};
// 添加内容的方法
const addTextContent = () => {
  if (textContent.value.trim()) {
    const existingIndex = contentItems.value.findIndex(item => item.type === 'text')
    const newItem = {
      type: 'text',
      content: textContent.value.trim()
    } as MessageVO
    if (existingIndex !== -1) {
      contentItems.value[existingIndex] = newItem
      ElMessage.success('文字内容更新成功')
    } else {
      contentItems.value.push(newItem)
      ElMessage.success('文字内容添加成功')
    }
    
    textDialogVisible.value = false
  } else {
    ElMessage.warning('请输入文字内容')
  }
}

const addImageContent = () => {
  if (imagePreview.value) {
    // 查找是否已存在图片类型的内容
    const existingIndex = contentItems.value.findIndex(item => item.type === 'image')
    
    const newItem = {
      type: 'image',
      content: imagePreview.value
    } as MessageVO
    
    if (existingIndex !== -1) {
      // 如果存在，则替换
      contentItems.value[existingIndex] = newItem
      ElMessage.success('图片替换成功')
    } else {
      // 如果不存在，则添加
      contentItems.value.push(newItem)
      ElMessage.success('图片添加成功')
    }
    
    imageDialogVisible.value = false
  }
}

const addGroupLinkContent = () => {
  if (groupLinkForm.link) {
    // 查找是否已存在群组链接类型的内容
    const existingIndex = contentItems.value.findIndex(item => item.type === 'groupLink')
    
    const newItem = {
      type: 'groupLink',
      // link: groupLinkForm.link,
      avatar: groupLinkForm.avatar,
      content: groupLinkForm.groupName,
      // groupText: groupLinkForm.groupText
    } as MessageVO
    
    if (existingIndex !== -1) {
      // 如果存在，则替换
      contentItems.value[existingIndex] = newItem
      ElMessage.success('群组链接替换成功')
    } else {
      // 如果不存在，则添加
      contentItems.value.push(newItem)
      ElMessage.success('群组链接添加成功')
    }
    
    groupLinkDialogVisible.value = false
  }
}

const addVoiceContent = () => {
  if (voicePreview.value) {
    // 查找是否已存在语音类型的内容
    const existingIndex = contentItems.value.findIndex(item => item.type === 'voice')
    
    const newItem = {
      type: 'voice',
      content: voicePreview.value
    } as MessageVO
    
    if (existingIndex !== -1) {
      // 如果存在，则替换
      contentItems.value[existingIndex] = newItem
      ElMessage.success('语音替换成功')
    } else {
      // 如果不存在，则添加
      contentItems.value.push(newItem)
      ElMessage.success('语音添加成功')
    }
    
    voiceDialogVisible.value = false
  }
}

const addCardContent = () => {
  if (cardContent.value.trim()) {
    // 查找是否已存在名片类型的内容
    const existingIndex = contentItems.value.findIndex(item => item.type === 'card')
    
    const newItem = {
      type: 'card',
      content: cardContent.value
    } as MessageVO
    
    if (existingIndex !== -1) {
      // 如果存在，则替换
      contentItems.value[existingIndex] = newItem
      ElMessage.success('名片替换成功')
    } else {
      // 如果不存在，则添加
      contentItems.value.push(newItem)
      ElMessage.success('名片添加成功')
    }
    
    cardDialogVisible.value = false
  } else {
    ElMessage.warning('请输入名片信息')
  }
}

const addImgTextContent = () => {
  if (imgTextForm) {
    // 查找是否已存在图文类型的内容
    const existingIndex = contentItems.value.findIndex(item => item.type === 'imgText')
    
    const newItem = {
      type: 'imgText',
      contentObj: imgTextForm
    } as MessageVO
    
    if (existingIndex !== -1) {
      // 如果存在，则替换
      contentItems.value[existingIndex] = newItem
      ElMessage.success('图文替换成功')
    } else {
      // 如果不存在，则添加
      contentItems.value.push(newItem)
      ElMessage.success('图文添加成功')
    }
    
    imgTextDialogVisible.value = false
  } else {
    ElMessage.warning('请输入图文信息')
  }
}

const addButtonContent = () => {
  buttonFormRef.value?.validate()
  if (buttonForm.text.trim() && buttonForm.businessId.trim()) {
    // 查找是否已存在超链类型的内容
    const existingIndex = contentItems.value.findIndex(item => item.type === 'button')
    
    const newItem = {
      type: 'button',
      contentObj: buttonForm
    } as MessageVO
    
    if (existingIndex !== -1) {
      // 如果存在，则替换
      contentItems.value[existingIndex] = newItem
      ElMessage.success('转发超链消息替换成功')
    } else {
      // 如果不存在，则添加
      contentItems.value.push(newItem)
      ElMessage.success('转发超链消息添加成功')
    }
    
    buttonDialogVisible.value = false
  }
}

// 文件处理方法
// const handleImageChange = (file: any) => {
//   const reader = new FileReader()
//   reader.onload = (e) => {
//     imagePreview.value = e.target?.result as string
//   }
//   reader.readAsDataURL(file.raw)
// }

// 解析群组链接
const parseGroupLink = async () => {
  if (!groupLinkForm.link) {
    ElMessage.warning('请输入群组链接')
    return
  }
  
  try {
    // 这里应该调用实际的API来解析群组链接
    // 模拟解析结果
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟解析结果
    groupLinkForm.avatar = 'https://via.placeholder.com/50x50?text=群'
    groupLinkForm.groupName = '示例群组名称'
    
    ElMessage.success('群组链接解析成功')
  } catch (error) {
    ElMessage.error('群组链接解析失败')
  }
}

// 获取内容类型标签
const getContentTypeLabel = (type: string) => {
  const labels = {
    text: '文字消息',
    image: '图片消息',
    groupLink: '群组链接',
    voice: '语音消息',
    card: '名片消息',
    imgText: '图文消息',
    button: '转发超链'
  }
  return labels[type] || type
}

// 删除内容项
const removeContentItem = (index: number) => {
  contentItems.value.splice(index, 1)
  ElMessage.success('内容删除成功')
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()

   // 手动重置所有字段
  Object.assign(formData, {
    tagId: '',
    groupId: '',
    mobile: '',
    taskName: '',
    intervalTime: 10,
    resourceType: 'upload',
    fileContent: '',
    blackTech: 0,
    sendContent: '',
    singleAccountNewCount: 1,
    startTime: '',
    messageContents: []
  })
  
  // 重置内容相关的数据
  contentItems.value = []
  textContent.value = ''
  imagePreview.value = ''
  voicePreview.value = ''
  cardContent.value = ''
  
  // 重置群组链接表单
  Object.assign(groupLinkForm, {
    groupText: '',
    avatar: '',
    content: ''
  })

  formData.fileContent = ''
}

// 打开弹窗
const open = () => {
  dialogVisible.value = true
  resetForm()
}

// 暴露方法
defineExpose({
  open
})
const handleImageChange = (file) => {
  if (!file) return;  
  selectedFile.value = file.raw;
  const reader = new FileReader();
  reader.onload = (e) => {
    imagePreview.value = e.target?.result as string
  };
  reader.readAsDataURL(selectedFile.value);
};
// 上传图片到OSS
const uploadImgToOss = async (type) => {
  await imgTextFormRef.value?.validate()

  if (!selectedFile.value) {
    ElMessage.error('请选择图片');
    return;
  }
  
  uploading.value = true;
  uploadProgress.value = 0;
  
  try {
    const formData = new FormData();
    formData.append('file', selectedFile.value);
    formData.append('folder', 'img-msg');
    
    // 调用OSS上传API
    const res = await OssApi.uploadFile(formData);
    imagePreview.value = res;
    if (type == 1) {
      addImageContent();
      imageDialogVisible.value = false;
    } else if (type == 2) {
      imgTextForm.imageUrl = imagePreview.value
      addImgTextContent();
      imgTextDialogVisible.value = false;
    }
  } catch (error) {
    console.error('上传失败:', error);
    ElMessage.error('上传失败，请重试');
  } finally {
    uploading.value = false;
    // 重置状态
    selectedFile.value = null;
    imagePreview.value = '';
  }
};

// 关闭图片选择对话框
const closeImgDialog = () => {
  // 重置状态
  imageDialogVisible.value = false;
  imagePreview.value = '';
  selectedFile.value = null;
  uploading.value = false;
  uploadProgress.value = 0;
};
// 关闭图片选择对话框
const closeImgTextDialog = () => {
  imgTextDialogVisible.value = false;
  imagePreview.value = '';
  selectedFile.value = null;
  uploading.value = false;
  uploadProgress.value = 0;
};
const handleVoiceChange = (file) => {
  if (!file) return;  
  selectedVoice.value = file.raw;
  const reader = new FileReader();
  reader.onload = (e) => {
    voicePreview.value = e.target?.result as string
  };
  reader.readAsDataURL(selectedVoice.value);
};
// 上传语音到OSS
const uploadVoiceToOss = async () => {
  if (!selectedVoice.value) {
    ElMessage.error('请选择语音');
    return;
  }
  
  uploading.value = true;
  
  try {
    const formData = new FormData();
    formData.append('file', selectedVoice.value);
    formData.append('folder', 'voice-msg'); 
    formData.append('messageType', 'voice');     
    // 调用OSS上传API
    const res = await OssApi.uploadFile(formData);
    voicePreview.value = res;
    addVoiceContent();
    voiceDialogVisible.value = false;
    
  } catch (error) {
    console.error('上传失败:', error);
    ElMessage.error('上传失败，请重试');
  } finally {
    uploading.value = false;
    // 重置状态
    selectedVoice.value = null;
    voicePreview.value = '';
  }
};

// 关闭语音选择对话框
const closeVoiceDialog = () => {
  // 重置状态
  voiceDialogVisible.value = false;
  voicePreview.value = '';
  selectedVoice.value = null;
  uploading.value = false;
};
// 页面加载时初始化
onMounted(() => {
  getTagList()
  getGroupList()
})
</script>

<style scoped>
.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.send-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.dialog-footer {
  text-align: right;
}

.content-preview {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  margin-top: 10px;
  max-height: 300px;
  overflow-y: auto;
  width: 100%;
}

.content-item {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
}

.content-item:last-child {
  margin-bottom: 0;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 5px;
  border-bottom: 1px solid #f0f0f0;
}

.content-type {
  font-weight: 500;
  color: #409eff;
  font-size: 12px;
}

.content-body {
  margin-top: 8px;
}

.text-content {
  white-space: pre-wrap;
  word-break: break-word;
}

.image-content {
  text-align: center;
}

.group-link-content .group-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.group-avatar {
  width: 40px;
  height: 40px;
  border-radius: 4px;
}

.group-details .group-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.group-details .group-link {
  font-size: 12px;
  color: #999;
  word-break: break-all;
}

.voice-content {
  text-align: center;
}

.card-content {
  white-space: pre-wrap;
  word-break: break-word;
}
.imgText-content {
  white-space: pre-wrap;
  word-break: break-word;
}
.display-area {
  max-width: 800px;
  margin: 40px auto;
}

.content-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  position: relative;
}

.item-title {
  margin-top: 0;
  color: #1890ff;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.item-image img {
  max-width: 100%;
  border-radius: 4px;
  margin: 10px 0;
}

.item-body {
  margin: 15px 0;
  line-height: 1.6;
}

.item-footer {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid #eee;
  color: #666;
}
.button-content {
  white-space: pre-wrap;
  word-break: break-word;
}
/* 解决左侧间距问题的核心样式 */
.item-buttons {
  margin: 15px 0;
  padding-left: 0 !important; /* 清除容器左侧内边距 */
}

.block-button {
  display: block;
  width: auto;
  margin-bottom: 10px !important;
  margin-right: 0 !important;
  margin-left: 0 !important; /* 强制清除左侧外边距 */
  padding-left: 15px; /* 可根据需要调整按钮内部左侧 padding */
  padding-right: 15px;
}

/* 若使用的是Element UI/Plus，可能需要深度选择器覆盖组件默认样式 */
:deep(.el-button.block-button) {
  margin-left: 0 !important;
}

.image-preview {
  text-align: center;
  margin-top: 10px;
}

.voice-preview {
  margin-top: 10px;
}

:deep(.el-upload) {
  display: inline-block;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-select .el-input) {
  width: 100%;
}
.button-group {
  border: 1px dashed #ccc;
  padding: 15px;
  border-radius: 4px;
}

.button-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.button-input {
  flex: 1;
}
</style>
