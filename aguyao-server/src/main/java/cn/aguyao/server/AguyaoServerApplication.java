package cn.aguyao.server;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 项目的启动类
 *
 * <AUTHOR>
@EnableConfigurationProperties
@EnableAspectJAutoProxy
@EnableScheduling
@SuppressWarnings("SpringComponentScan") // 忽略 IDEA 无法识别 ${aguyao.info.base-package}
@SpringBootApplication(scanBasePackages = {"${aguyao.info.base-package}.server", "${aguyao.info.base-package}.module"})
public class AguyaoServerApplication {

    public static void main(String[] args) {

        SpringApplication.run(AguyaoServerApplication.class, args);
//        new SpringApplicationBuilder(AguyaoServerApplication.class)
//                .applicationStartup(new BufferingApplicationStartup(20480))
//                .run(args);

    }

    private static void genPass() {
        // 创建PasswordEncoder实例
        PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

        // 原始密码
        String rawPassword = "kf1234"; // 替换为你要加密的密码

        // 加密密码
        String encodedPassword = passwordEncoder.encode(rawPassword);

        System.out.println("原始密码: " + rawPassword);
        System.out.println("加密后的密码: " + encodedPassword);
    }

}
