package cn.aguyao.module.wsapp.service.custserviceaccount;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.wsapp.controller.admin.custserviceaccount.vo.*;
import cn.aguyao.module.wsapp.dal.dataobject.custserviceaccount.CustServiceAccountDO;
import cn.aguyao.module.wsapp.dal.dataobject.group.GroupDO;
import cn.aguyao.module.wsapp.dal.mysql.custserviceaccount.CustServiceAccountMapper;
import cn.aguyao.module.wsapp.service.apiservice.GroupBasicService;
import cn.aguyao.module.wsapp.service.basegroup.BaseGroupService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.util.*;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.CUST_SERVICE_ACCOUNT_NOT_EXISTS;

/**
 * 客服账号信息表 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class CustServiceAccountServiceImpl implements CustServiceAccountService {

    @Resource
    private CustServiceAccountMapper custServiceAccountMapper;
    @Autowired
    private GroupBasicService groupBasicService;
    @Resource
    private BaseGroupService baseGroupService;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Override
    public Long createCustServiceAccount(CustServiceAccountSaveReqVO createReqVO) {
        // 插入
        CustServiceAccountDO custServiceAccount = BeanUtils.toBean(createReqVO, CustServiceAccountDO.class);

        if (StrUtil.isNotBlank(custServiceAccount.getPassword())) {
            String s = passwordEncoder.encode(custServiceAccount.getPassword());
            custServiceAccount.setPassword(s);
        }

        custServiceAccountMapper.insert(custServiceAccount);
        // 返回
        return custServiceAccount.getId();
    }

    @Override
    public void updateCustServiceAccount(CustServiceAccountSaveReqVO updateReqVO) {
        // 校验存在
        validateCustServiceAccountExists(updateReqVO.getId());
        // 更新
        CustServiceAccountDO updateObj = BeanUtils.toBean(updateReqVO, CustServiceAccountDO.class);
        if (StrUtil.isNotBlank(updateObj.getPassword())) {
            String s = passwordEncoder.encode(updateObj.getPassword());
            updateObj.setPassword(s);
        }
        custServiceAccountMapper.updateById(updateObj);
    }

    @Override
    public void deleteCustServiceAccount(Long id) {
        // 校验存在
        validateCustServiceAccountExists(id);
        // 删除
        custServiceAccountMapper.deleteById(id);
    }

    private void validateCustServiceAccountExists(Long id) {
        if (custServiceAccountMapper.selectById(id) == null) {
            throw exception(CUST_SERVICE_ACCOUNT_NOT_EXISTS);
        }
    }

    @Override
    public CustServiceAccountDO getCustServiceAccount(Long id) {
        return custServiceAccountMapper.selectById(id);
    }

    @Override
    public PageResult<CustServiceAccountDO> getCustServiceAccountPage(CustServiceAccountPageReqVO pageReqVO) {
        PageResult<CustServiceAccountDO> pageResult = custServiceAccountMapper.selectPage(pageReqVO);

        List<GroupDO> groupList = baseGroupService.getGroupList();
        Map<Long, String> groupNameMap = new HashMap<>();
        for (GroupDO groupDO : groupList) {
            groupNameMap.put(groupDO.getId(), groupDO.getGroupName());
        }

        pageResult.getList().forEach(item -> {
            if (StrUtil.isNotBlank(item.getManagementGroup())) {
                String[] split = item.getManagementGroup().split(",");
                StringBuilder stringBuffer = new StringBuilder();
                for (String s : split) {
                    if (StrUtil.isBlank(s)) {
                        continue;
                    }
                    stringBuffer.append(groupNameMap.get(Long.valueOf(s))).append(",");
                }
                if (stringBuffer.lastIndexOf(",") == stringBuffer.length() - 1) {
                    stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                }
                item.setManagementGroupStr(stringBuffer.toString());
            }
        });

        return pageResult;
    }

    @Override
    public void updateCustServiceAccountSwitch(CustServiceAccountSwitchReqVO updateReqVO) {
        if (Objects.isNull(updateReqVO) || Objects.isNull(updateReqVO.getId())) {
            return ;
        }
        CustServiceAccountDO updateObj = null;
        switch (updateReqVO.getType()) {
            case 1:
                updateObj = new CustServiceAccountDO();
                updateObj.setId(updateReqVO.getId());
                updateObj.setNewFriends(updateReqVO.getSwitchBtn());
                custServiceAccountMapper.updateById(updateObj);
                break;
            case 2:
                updateObj = new CustServiceAccountDO();
                updateObj.setId(updateReqVO.getId());
                updateObj.setAccountRegistration(updateReqVO.getSwitchBtn());
                custServiceAccountMapper.updateById(updateObj);
                break;
            case 3:
                updateObj = new CustServiceAccountDO();
                updateObj.setId(updateReqVO.getId());
                updateObj.setFriendTransfer(updateReqVO.getSwitchBtn());
                custServiceAccountMapper.updateById(updateObj);
                break;
            default:
                log.info("updateCustServiceAccountSwitch: type is error");
                break;
        }
    }

    @Override
    public void updateBatch(CustServiceBatchSettingReqVO updateReqVO) {

        if (Objects.isNull(updateReqVO) || CollUtil.isEmpty(updateReqVO.getIds())) {
            log.info("updateBatch: updateReqVO is null or ids is empty");
            return ;
        }

        List<CustServiceAccountDO> updateList = new ArrayList<>();
        for (Long id : updateReqVO.getIds()) {
            CustServiceAccountDO updateObj = new CustServiceAccountDO();
            updateObj.setId(id);
            updateObj.setDailyFetchLimit(updateReqVO.getDailyFetchLimit());
            updateObj.setEntryTranslation(updateReqVO.getEntryTranslation());
            updateObj.setExitTranslation(updateReqVO.getExitTranslation());
            updateObj.setGroupTranslation(updateReqVO.getGroupTranslation());

            updateList.add(updateObj);
        }

        custServiceAccountMapper.updateBatch(updateList);
    }


    @Override
    public void deleteBatch(List<Long> ids) {
        custServiceAccountMapper.deleteBatchIds(ids);
    }

    @Override
    public void updateCustServiceAccountGroup(CustServiceSettingGroupReqVO updateReqVO) {
        if (Objects.isNull(updateReqVO) || Objects.isNull(updateReqVO.getId()) ) {
            log.info("updateCustServiceAccountGroup: updateReqVO is null or id is null");
            return ;
        }

        StringBuffer sb = new StringBuffer();
        for (Long groupId : updateReqVO.getGroupIds()) {
            sb.append(groupId).append(",");
        }

        if (sb.lastIndexOf( ",") == sb.length() - 1) {
            sb.deleteCharAt(sb.length() - 1);
        }

        CustServiceAccountDO updateObj = new CustServiceAccountDO();
        updateObj.setId(updateReqVO.getId());
        updateObj.setManagementGroup(sb.toString());
        custServiceAccountMapper.updateById(updateObj);
    }

}