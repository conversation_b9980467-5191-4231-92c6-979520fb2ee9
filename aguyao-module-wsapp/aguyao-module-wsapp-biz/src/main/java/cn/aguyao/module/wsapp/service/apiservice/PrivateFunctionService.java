package cn.aguyao.module.wsapp.service.apiservice;

import cn.aguyao.framework.common.util.json.JsonUtils;
import cn.aguyao.module.charging.enums.wsapp.ApiRequestUri;
import cn.aguyao.module.charging.pojo.ApiResponse;
import cn.aguyao.module.wsapp.dal.dataobject.quickgroupsenddetail.QuickGroupSendDetailDO;
import cn.aguyao.module.wsapp.dto.psendtext.*;
import cn.aguyao.module.wsapp.service.cache.SessionInitCacheService;
import cn.aguyao.module.wsapp.service.quickgroupsenddetail.QuickGroupSendDetailService;
import cn.aguyao.module.wsapp.service.scheduler.TaskExecutionService;
import cn.aguyao.module.wsapp.util.MediaTypeUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.log4j.Log4j2;
import okhttp3.*;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Log4j2
@Service
public class PrivateFunctionService {

    @Resource
    private OkHttpClient okHttpClient;

    @Resource
    private ApiBasicService apiBasicService;

    @Resource
    private SessionInitCacheService sessionInitCacheService;

    @Lazy
    @Resource
    private QuickGroupSendDetailService quickGroupSendDetailService;


    /**
     * 初始化会话
     * @param sendMobile 发送人手机号
     * @param receiveMobile 接收人手机号
     */
    public ApiResponse initSession(String sendMobile, String receiveMobile) {
        // 先检查缓存中是否已经有成功的初始化结果
        if (sessionInitCacheService.isInitSuccess(sendMobile, receiveMobile)) {
            log.info("[initSession][缓存命中] sendMobile={}, receiveMobile={}, 跳过重复初始化", sendMobile, receiveMobile);
            return null;
        }

        log.info("[initSession][开始初始化会话] sendMobile={}, receiveMobile={}", sendMobile, receiveMobile);

        JSONObject userObject = new JSONObject();
        userObject.put("ToUserId", receiveMobile);
        JSONObject cgiRequest = new JSONObject();
        cgiRequest.put("CgiRequest", userObject);

        String url = apiBasicService.getWSUrL(ApiRequestUri.ReqUri4PrivateEnum.INIT_SESSION) + sendMobile;
        RequestBody body = RequestBody.create(cgiRequest.toString(), MediaTypeUtil.JSON);
        Request request = new Request.Builder()
                .url(url)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();

        try {
            try (Response response = okHttpClient.newCall(request).execute()) {
                ResponseBody responseBody = response.body();

                if (response.code() == 200 && Objects.nonNull(responseBody)) {
                    // 只读取一次响应体内容
                    String responseContent = responseBody.string();
                    log.info("[initSession][API响应] sendMobile={}, receiveMobile={}, response={}",
                            sendMobile, receiveMobile, responseContent);

                    if (StrUtil.isNotBlank(responseContent)) {
                        ApiResponse apiResponse = JsonUtils.parseObject(responseContent, ApiResponse.class);
                        boolean success = apiResponse != null && apiResponse.getSuccess();

                        // 将结果保存到缓存中
                        sessionInitCacheService.putInitResult(sendMobile, receiveMobile, success);

                        if (success) {
                            log.info("[initSession][初始化成功并缓存] sendMobile={}, receiveMobile={}", sendMobile, receiveMobile);
                        } else {
                            log.warn("[initSession][初始化失败] sendMobile={}, receiveMobile={}, response={}",
                                    sendMobile, receiveMobile, responseContent);
                        }

                        return apiResponse;
                    }
                }
            }
        } catch (Exception e) {
            log.error("[initSession][初始化会话异常] sendMobile={}, receiveMobile={}, error={}",
                    sendMobile, receiveMobile, e.getMessage(), e);
            // 异常情况下也缓存失败结果，避免频繁重试
            sessionInitCacheService.putInitResult(sendMobile, receiveMobile, false);
        }

        log.warn("[initSession][初始化失败] sendMobile={}, receiveMobile={}", sendMobile, receiveMobile);
        return null;
    }

    /**
     * 发送文本
     * @param sendTextReq 消息内容
     */
    @Async
    public void callSendTextApi(Long detailId, String sendMobile, SendTextReq sendTextReq) {

        ApiResponse result = initSession(sendMobile, sendTextReq.getToUserId());
        if (result== null || BooleanUtil.isFalse(result.getSuccess())) {
            // 初始化失败，不执行
            log.warn("[callSendTextApi][初始化失败，不执行发送] sendMobile={}, receiveMobile={}", sendMobile, sendTextReq.getToUserId());
            return ;
        }

        JSONObject cgiRequest = new JSONObject();
        cgiRequest.put("CgiRequest", sendTextReq);

        String content = JsonUtils.toJsonPrettyString(cgiRequest);

        String url = apiBasicService.getWSUrL(ApiRequestUri.ReqUri4PrivateEnum.SEND_TEXT)  +  sendMobile;
        RequestBody body = RequestBody.create(content, MediaTypeUtil.JSON);
        Request request = new Request.Builder()
                .url(url)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        try {
            try {
                try (Response response = okHttpClient.newCall(request).execute()) {
                    // 业务， 更新发送结果
                    ResponseBody responseBody = response.body();
                    if (response.code() == 200 && Objects.nonNull(responseBody)) {
                        // 只读取一次响应体内容
                        String responseContent = responseBody.string();
                        log.info("发送文本结果：{}", responseContent);

                        ApiResponse apiResponse = JsonUtils.parseObject(responseContent, ApiResponse.class);
                        if (Objects.nonNull(apiResponse)) {
                            // 更新发送状态
                            quickGroupSendDetailService.updateSendStatus(detailId, apiResponse.getSuccess(),apiResponse.getErrMsg(),apiResponse.getResponseData() == null ? "" : apiResponse.getResponseData().getID(), QuickGroupSendDetailDO.MSG_TYPE_TEXT);
                        }
                    }
                }
            } catch (Exception e) {
                quickGroupSendDetailService.updateSendStatus(detailId, false,e.getMessage(),"", QuickGroupSendDetailDO.MSG_TYPE_TEXT);
                log.error("发送文本失败：{}", e.getMessage());
            }
        } catch (Exception e) {
            quickGroupSendDetailService.updateSendStatus(detailId, false,e.getMessage(),"", QuickGroupSendDetailDO.MSG_TYPE_TEXT);
            log.error("发送文本失败：{}", e.getMessage());
        }

    }

    /**
     * 发送图片
     * @param sendImgReq 消息内容
     */
    @Async
    public void callSendImgApi(Long detailId, String sendMobile, SendImgReq sendImgReq) {

        ApiResponse result = initSession(sendMobile, sendImgReq.getToUserId());
        if (result == null || BooleanUtil.isFalse(result.getSuccess())) {
            // 初始化失败，不执行
            log.warn("[callSendImgApi][初始化失败，不执行发送] sendMobile={}, receiveMobile={}", sendMobile, sendImgReq.getToUserId());
            return ;
        }

        JSONObject cgiRequest = new JSONObject();
        cgiRequest.put("CgiRequest", sendImgReq);

        String content = JsonUtils.toJsonPrettyString(cgiRequest);

        String url = apiBasicService.getWSUrL(ApiRequestUri.ReqUri4PrivateEnum.SEND_IMAGE)  +  sendMobile;
        RequestBody body = RequestBody.create(content, MediaTypeUtil.JSON);
        Request request = new Request.Builder()
                .url(url)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        try {
            try {
                try (Response response = okHttpClient.newCall(request).execute()) {
                    // 业务， 更新发送结果
                    ResponseBody responseBody = response.body();
                    if (response.code() == 200 && Objects.nonNull(responseBody)) {
                        // 只读取一次响应体内容
                        String responseContent = responseBody.string();
                        log.info("发送图片结果：{}", responseContent);

                        ApiResponse apiResponse = JsonUtils.parseObject(responseContent, ApiResponse.class);
                        if (Objects.nonNull(apiResponse)) {
                            // 更新发送状态
                            quickGroupSendDetailService.updateSendStatus(detailId, apiResponse.getSuccess(),apiResponse.getErrMsg(),apiResponse.getResponseData() == null ? "" : apiResponse.getResponseData().getID(), QuickGroupSendDetailDO.MSG_TYPE_IMG);

                        }
                    }
                }
            } catch (Exception e) {
                quickGroupSendDetailService.updateSendStatus(detailId, false,e.getMessage(),"",  QuickGroupSendDetailDO.MSG_TYPE_IMG);
                log.error("发送图片失败：{}", e.getMessage());
            }
        } catch (Exception e) {
            quickGroupSendDetailService.updateSendStatus(detailId, false,e.getMessage(),"",  QuickGroupSendDetailDO.MSG_TYPE_IMG);
            log.error("发送图片失败：{}", e.getMessage());
        }

    }
    /**
     * 发送语音
     * @param sendVoiceReq 消息内容
     */
    @Async
    public void callSendVoiceApi(Long detailId, String sendMobile, SendVoiceReq sendVoiceReq) {

        ApiResponse result = initSession(sendMobile, sendVoiceReq.getToUserId());
        if (result == null || BooleanUtil.isFalse(result.getSuccess())) {
            // 初始化失败，不执行
            log.warn("[callSendImgApi][初始化失败，不执行发送] sendMobile={}, receiveMobile={}", sendMobile, sendVoiceReq.getToUserId());
            return ;
        }

        JSONObject cgiRequest = new JSONObject();
        cgiRequest.put("CgiRequest", sendVoiceReq);

        String content = JsonUtils.toJsonPrettyString(cgiRequest);

        String url = apiBasicService.getWSUrL(ApiRequestUri.ReqUri4PrivateEnum.SEND_VOICE)  +  sendMobile;
        RequestBody body = RequestBody.create(content, MediaTypeUtil.JSON);
        Request request = new Request.Builder()
                .url(url)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        try {
            try {
                try (Response response = okHttpClient.newCall(request).execute()) {
                    // 业务， 更新发送结果
                    ResponseBody responseBody = response.body();
                    if (response.code() == 200 && Objects.nonNull(responseBody)) {
                        // 只读取一次响应体内容
                        String responseContent = responseBody.string();
                        log.info("发送语音结果：{}", responseContent);

                        ApiResponse apiResponse = JsonUtils.parseObject(responseContent, ApiResponse.class);
                        if (Objects.nonNull(apiResponse)) {
                            // 更新发送状态
                            quickGroupSendDetailService.updateSendStatus(detailId, apiResponse.getSuccess(),apiResponse.getErrMsg(),apiResponse.getResponseData() == null ? "" : apiResponse.getResponseData().getID(), QuickGroupSendDetailDO.MSG_TYPE_VOICE);

                        }
                    }
                }
            } catch (Exception e) {
                quickGroupSendDetailService.updateSendStatus(detailId, false,e.getMessage(),"",  QuickGroupSendDetailDO.MSG_TYPE_VOICE);
                log.error("发送语音失败：{}", e.getMessage());
            }
        } catch (Exception e) {
            quickGroupSendDetailService.updateSendStatus(detailId, false,e.getMessage(),"",  QuickGroupSendDetailDO.MSG_TYPE_VOICE);
            log.error("发送语音失败：{}", e.getMessage());
        }

    }
    /**
     * 发送名片
     * @param sendCardReq 消息内容
     */
    @Async
    public void callSendCardApi(Long detailId, String sendMobile, SendCardReq sendCardReq) {
        ApiResponse result = initSession(sendMobile, sendCardReq.getToUserId());
        if (result== null || BooleanUtil.isFalse(result.getSuccess())) {
            // 初始化失败，不执行
            log.warn("[callSendImgApi][初始化失败，不执行发送] sendMobile={}, receiveMobile={}", sendMobile, sendCardReq.getToUserId());
            return ;
        }

        JSONObject cgiRequest = new JSONObject();
        cgiRequest.put("CgiRequest", sendCardReq);

        String content = JsonUtils.toJsonPrettyString(cgiRequest);

        String url = apiBasicService.getWSUrL(ApiRequestUri.ReqUri4PrivateEnum.SEND_VCARD)  +  sendMobile;
        RequestBody body = RequestBody.create(content, MediaTypeUtil.JSON);
        Request request = new Request.Builder()
                .url(url)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        try {
            try {
                try (Response response = okHttpClient.newCall(request).execute()) {
                    // 业务， 更新发送结果
                    ResponseBody responseBody = response.body();
                    if (response.code() == 200 && Objects.nonNull(responseBody)) {
                        // 只读取一次响应体内容
                        String responseContent = responseBody.string();
                        log.info("发送名片结果：{}", responseContent);

                        ApiResponse apiResponse = JsonUtils.parseObject(responseContent, ApiResponse.class);
                        if (Objects.nonNull(apiResponse)) {
                            // 更新发送状态
                            quickGroupSendDetailService.updateSendStatus(detailId, apiResponse.getSuccess(),apiResponse.getErrMsg(),apiResponse.getResponseData() == null ? "" : apiResponse.getResponseData().getID(), QuickGroupSendDetailDO.MSG_TYPE_CARD);

                        }
                    }
                }
            } catch (Exception e) {
                quickGroupSendDetailService.updateSendStatus(detailId, false,e.getMessage(),"",  QuickGroupSendDetailDO.MSG_TYPE_CARD);
                log.error("发送名片失败：{}", e.getMessage());
            }
        } catch (Exception e) {
            quickGroupSendDetailService.updateSendStatus(detailId, false,e.getMessage(),"",  QuickGroupSendDetailDO.MSG_TYPE_CARD);
            log.error("发送名片失败：{}", e.getMessage());
        }

    }
    /**
     * 发送图文消息
     * @param sendImgTextReq 消息内容
     */
    @Async
    public void callSendImgTextApi(Long detailId, String sendMobile, SendImgTextReq sendImgTextReq) {

        ApiResponse result = initSession(sendMobile, sendImgTextReq.getToUserId());
        if (result== null || BooleanUtil.isFalse(result.getSuccess())) {
            // 初始化失败，不执行
            log.warn("[callSendImgApi][初始化失败，不执行发送] sendMobile={}, receiveMobile={}", sendMobile, sendImgTextReq.getToUserId());
            return ;
        }

        JSONObject cgiRequest = new JSONObject();
        cgiRequest.put("CgiRequest", sendImgTextReq);

        String content = JsonUtils.toJsonPrettyString(cgiRequest);

        String url = apiBasicService.getWSUrL(ApiRequestUri.ReqUri4PrivateEnum.SEND_IMGTEXT)  +  sendMobile;
        RequestBody body = RequestBody.create(content, MediaTypeUtil.JSON);
        Request request = new Request.Builder()
                .url(url)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        try {
            try {
                try (Response response = okHttpClient.newCall(request).execute()) {
                    // 业务， 更新发送结果
                    ResponseBody responseBody = response.body();
                    if (response.code() == 200 && Objects.nonNull(responseBody)) {
                        // 只读取一次响应体内容
                        String responseContent = responseBody.string();
                        log.info("发送图文结果：{}", responseContent);

                        ApiResponse apiResponse = JsonUtils.parseObject(responseContent, ApiResponse.class);
                        if (Objects.nonNull(apiResponse)) {
                            // 更新发送状态
                            quickGroupSendDetailService.updateSendStatus(detailId, apiResponse.getSuccess(),apiResponse.getErrMsg(),apiResponse.getResponseData() == null ? "" : apiResponse.getResponseData().getID(), QuickGroupSendDetailDO.MSG_TYPE_IMGTEXT);

                        }
                    }
                }
            } catch (Exception e) {
                quickGroupSendDetailService.updateSendStatus(detailId, false,e.getMessage(),"",  QuickGroupSendDetailDO.MSG_TYPE_IMGTEXT);
                log.error("发送图文失败：{}", e.getMessage());
            }
        } catch (Exception e) {
            quickGroupSendDetailService.updateSendStatus(detailId, false,e.getMessage(),"",  QuickGroupSendDetailDO.MSG_TYPE_IMGTEXT);
            log.error("发送图文失败：{}", e.getMessage());
        }

    }
}
