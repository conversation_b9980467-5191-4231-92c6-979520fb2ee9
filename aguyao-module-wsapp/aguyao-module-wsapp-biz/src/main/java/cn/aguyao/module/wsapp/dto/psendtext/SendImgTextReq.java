package cn.aguyao.module.wsapp.dto.psendtext;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

import java.util.List;

/**
 * 跟人消息， 发送图文
 */
@Data
@Builder
public class SendImgTextReq {

    @JsonProperty("ToUserId")
    private String toUserId;

    @JsonProperty("IsGroup")
    private boolean isGroup;

    @JsonProperty("Title")
    private String title;

    @JsonProperty("Body")
    private String body;

    @JsonProperty("Footer")
    private String footer;

    @JsonProperty("ImageUrl")
    private String imageUrl;

    @JsonProperty("Buttons")
    private List<ButtonItem> buttons;
    /**
     * 黑科技发消息true启用false常规
     */
    @JsonProperty("ContextInfo")
    private String contextInfo;

    @Getter
    @AllArgsConstructor
    public enum ContextInfoEnum {
        CONTACT_CARD("contact_card", "联系人卡片"),
        CONTACT_SEARCH("contact_search", "联系人搜索"),
        GLOBAL_SEARCH_NEW_CHAT("global_search_new_chat", "全局搜索新消息"),
        PHONE_NUMBER_HYPERLINK("phone_number_hyperlink", "电话号码超链接"),
        GROUP_PARTICIPANT_LIST("group_participant_list", "群成员列表");

        private final String type;
        private final String desc;
    }
}
