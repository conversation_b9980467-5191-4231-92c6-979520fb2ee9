package cn.aguyao.module.wsapp.dal.dataobject.impdata;

import cn.aguyao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 数据导入 DO
 *
 * <AUTHOR>
 */
@TableName("wsapp_source_data")
@KeySequence("wsapp_source_data_seq")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImpdataDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 数据行
     */
    private String data;

    @TableLogic
    private Boolean deleted;

    private LocalDateTime createTime;
}