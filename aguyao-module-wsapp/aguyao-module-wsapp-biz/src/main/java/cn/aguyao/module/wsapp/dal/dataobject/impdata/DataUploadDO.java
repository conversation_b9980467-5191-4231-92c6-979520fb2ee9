package cn.aguyao.module.wsapp.dal.dataobject.impdata;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 数据导入 DO
 *
 * <AUTHOR>
 */
@TableName("wsapp_data_upload")
@KeySequence("wsapp_data_upload_seq")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataUploadDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    private String hideVersion;

    private String lessVersion;

    @TableLogic
    private Boolean deleted;

    private LocalDateTime createTime;
}