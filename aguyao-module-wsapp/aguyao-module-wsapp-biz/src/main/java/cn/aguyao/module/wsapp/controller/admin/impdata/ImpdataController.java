package cn.aguyao.module.wsapp.controller.admin.impdata;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.wsapp.controller.admin.baseaccount.vo.AccountRespVO;
import cn.aguyao.module.wsapp.controller.admin.impdata.vo.ImpdataPageReqVO;
import cn.aguyao.module.wsapp.controller.admin.impdata.vo.ImpdataReqVO;
import cn.aguyao.module.wsapp.controller.admin.impdata.vo.ImpdataRespVO;
import cn.aguyao.module.wsapp.controller.admin.impdata.vo.UploadReqVO;
import cn.aguyao.module.wsapp.dal.dataobject.account.AccountDO;
import cn.aguyao.module.wsapp.dal.dataobject.impdata.DataUploadDO;
import cn.aguyao.module.wsapp.dal.dataobject.impdata.ImpdataDO;
import cn.aguyao.module.wsapp.service.impdata.ImpdataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 账号管理")
@RestController
@RequestMapping("/wsapp/querydata")
@Validated
public class ImpdataController {
    @Resource
    ImpdataService impdataService;

    @PostMapping("/impdata")
    @Operation(summary = "数据导入")
    @PreAuthorize("@ss.hasPermission('wsapp:imp:query')")
    public CommonResult<String> impdata(@Valid @RequestBody ImpdataReqVO impdataReqVO) {
        return success(impdataService.impdata(impdataReqVO));
    }

    @GetMapping("/getImpdataPage")
    @Operation(summary = "数据列表分页")
    @PreAuthorize("@ss.hasPermission('wsapp:imp:query')")
    public CommonResult<PageResult<ImpdataRespVO>> getImpdataPage(@Valid ImpdataPageReqVO pageReqVO) {
        PageResult<ImpdataDO> pageResult = impdataService.getImpdataPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ImpdataRespVO.class));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除")
    @PreAuthorize("@ss.hasPermission('wsapp:imp:delete')")
    public CommonResult<Boolean> deleteGroup(@RequestParam("id") Long id) {
        impdataService.deleteGroup(id);
        return success(true);
    }

    @DeleteMapping("/batch-delete")
    @Operation(summary = "批量删除")
    @Parameter(name = "ids", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('wsapp:imp:delete')")
    public CommonResult<Boolean> batchDelete(@RequestParam("ids") List<Long> ids) {
        impdataService.batchDelete(ids);
        return success(true);
    }

    @PostMapping("/uploadHide")
    @Operation(summary = "隐根上传")
    @PreAuthorize("@ss.hasPermission('wsapp:imp:query')")
    public  CommonResult<Boolean> uploadHide(UploadReqVO dto) {
        try {
            // 1. 获取上传的文件
            MultipartFile file = dto.getFile();

            // 2. 处理文件名（避免重复，使用UUID+原文件名）
            String originalFilename = file.getOriginalFilename();
            String fileSuffix = "wapro.roothide.deb"; // 获取文件后缀

            // 3. 确保存储目录存在
            String uploadPath = "/www/wwwroot/file";
            File directory = new File(uploadPath);
            if (!directory.exists()) {
                directory.mkdirs(); // 递归创建目录
            }

            // 4. 保存文件到服务器
            Path filePath = Paths.get(uploadPath, fileSuffix);
            Files.write(filePath, file.getBytes());

            // 5. 更新版本
            impdataService.updateVersion(1,dto.getHideVersion());

            return success(true,"隐根文件上传成功");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @PostMapping("/uploadLess")
    @Operation(summary = "无根上传")
    @PreAuthorize("@ss.hasPermission('wsapp:imp:query')")
    public  CommonResult<Boolean> uploadLess(UploadReqVO dto) {
        try {
            // 1. 获取上传的文件
            MultipartFile file = dto.getFile();

            // 2. 处理文件名（避免重复，使用UUID+原文件名）
            String originalFilename = file.getOriginalFilename();
            String fileSuffix = "wapro.rootless.deb"; // 获取文件后缀

            // 3. 确保存储目录存在
            String uploadPath = "/www/wwwroot/file";
            File directory = new File(uploadPath);
            if (!directory.exists()) {
                directory.mkdirs(); // 递归创建目录
            }

            // 4. 保存文件到服务器
            Path filePath = Paths.get(uploadPath, fileSuffix);
            Files.write(filePath, file.getBytes());

            // 5. 更新版本
            impdataService.updateVersion(2,dto.getLessVersion());

            return success(true,"隐根文件上传成功");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @GetMapping("/getVersion")
    @Operation(summary = "数据导入")
    @PreAuthorize("@ss.hasPermission('wsapp:imp:query')")
    public CommonResult<DataUploadDO> impdata() {
        return success(impdataService.getVersion());
    }
}