package cn.aguyao.module.wsapp.controller.chat.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 客服账号信息表 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ChatCustServiceRespVO {

    @Schema(description = "主键ID，自增", requiredMode = Schema.RequiredMode.REQUIRED, example = "10325")
    @ExcelProperty("主键ID，自增")
    private Long id;

    @Schema(description = "账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "30734")
    @ExcelProperty("账号")
    private String account;
}