package cn.aguyao.module.wsapp.dal.mysql.impdata;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.wsapp.controller.admin.impdata.vo.ImpdataPageReqVO;
import cn.aguyao.module.wsapp.dal.dataobject.impdata.DataUploadDO;
import cn.aguyao.module.wsapp.dal.dataobject.impdata.ImpdataDO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 账号管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DataUploadMapper extends BaseMapperX<DataUploadDO> {

}