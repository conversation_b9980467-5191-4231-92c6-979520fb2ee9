package cn.aguyao.module.wsapp.controller.chat.user;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.security.core.util.SecurityFrameworkUtils;
import cn.aguyao.module.wsapp.controller.chat.user.vo.*;
import cn.aguyao.module.wsapp.dal.dataobject.custserviceaccount.CustServiceAccountDO;
import cn.aguyao.module.wsapp.service.custserviceaccount.CustServiceAccountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "客服账号信息表")
@RestController
@RequestMapping("/chat/cust-service")
@Validated
public class ChatCustServiceController {

    @Resource
    private CustServiceAccountService custServiceAccountService;

    @GetMapping("/user-info")
    @Operation(summary = "获得客服账号信息表")
    public CommonResult<ChatCustServiceRespVO> getCustServiceAccount() {
        Long id = SecurityFrameworkUtils.getLoginUserId();
        CustServiceAccountDO custServiceAccount = custServiceAccountService.getCustServiceAccount(id);
        return success(BeanUtils.toBean(custServiceAccount, ChatCustServiceRespVO.class));
    }

}