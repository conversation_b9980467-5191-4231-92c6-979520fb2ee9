package cn.aguyao.module.wsapp.dal.dataobject.quickgroupsenddetail;

import cn.aguyao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;


/**
 * 快打群发专属 DO
 *
 * <AUTHOR>
 */
@TableName("wsapp_quick_group_send_detail")
@KeySequence("wsapp_quick_group_send_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuickGroupSendDetailDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 任务编号
     */
    private Long taskId;

    /**
     * 子任务id
     */
    private Long subTaskId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 是否回复
     */
    private Integer isReply;

    /**
     * 执行结果
     */
    private Integer exeResult;

    /**
     * 执行结果信息
     */
    private String resultInfo;
    /**
     * 返回文本信息id
     */
    private String txtMessageId;
    /**
     * 返回图片信息id
     */
    private String imgMessageId;
    /**
     * 返回语音信息id
     */
    private String voiceMessageId;
    /**
     * 返回名片信息id
     */
    private String cardMessageId;
    /**
     * 返回图文信息id
     */
    private String imgtextMessageId;
    /**
     * 返回超链信息id
     */
    private String buttonMessageId;
    /**
     * 待执行
     */
    public static final int STATUS_PENDING = 0;

    /**
     * 执行成功
     */
    public static final int STATUS_SUCCESS = 1;

    /**
     * 执行失败
     */
    public static final int STATUS_FAIL = 2;
    /**
     * 文字消息
     */
    public static final int MSG_TYPE_TEXT = 1;

    /**
     * 图片消息
     */
    public static final int MSG_TYPE_IMG = 2;
    /**
     * 语音消息
     */
    public static final int MSG_TYPE_VOICE = 3;
    /**
     * 名片消息
     */
    public static final int MSG_TYPE_CARD = 4;
    /**
     * 图文消息
     */
    public static final int MSG_TYPE_IMGTEXT = 5;
    /**
     * 按钮消息
     */
    public static final int MSG_TYPE_BUTTON = 6;
    /**
     * 文字消息已送达
     */
    public static final String TEXT_RESULT_DELAY = "文字消息已送达";

    /**
     * 图片消息已送达
     */
    public static final String IMG_RESULT_DELAY = "图片消息已送达";
    /**
     * 语音消息已送达
     */
    public static final String VOICE_RESULT_DELAY = "语音消息已送达";
    /**
     * 名片消息已送达
     */
    public static final String CARD_RESULT_DELAY = "名片消息已送达";
    /**
     * 图文消息已送达
     */
    public static final String IMGTEXT_RESULT_DELAY = "图文消息已送达";
    /**
     * 超链消息已送达
     */
    public static final String BUTTON_RESULT_DELAY = "超链消息已送达";
    /**
     * 发消息的账号，或手机号
     */
    private String account;

    /**
     * 运行时间
     */
    @TableField(exist = false)
    private LocalDateTime runTime;

}