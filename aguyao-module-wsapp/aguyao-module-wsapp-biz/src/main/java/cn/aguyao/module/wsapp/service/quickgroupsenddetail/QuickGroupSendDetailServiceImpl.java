package cn.aguyao.module.wsapp.service.quickgroupsenddetail;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.wsapp.controller.admin.quickgroupsenddetail.vo.QuickGroupSendDetailPageReqVO;
import cn.aguyao.module.wsapp.controller.admin.quickgroupsenddetail.vo.QuickGroupSendDetailSaveReqVO;
import cn.aguyao.module.wsapp.dal.dataobject.quickgroupsendaccount.QuickGroupSendAccountDO;
import cn.aguyao.module.wsapp.dal.dataobject.quickgroupsenddetail.QuickGroupSendDetailDO;
import cn.aguyao.module.wsapp.dal.mysql.quickgroupsendaccount.QuickGroupSendAccountMapper;
import cn.aguyao.module.wsapp.dal.mysql.quickgroupsenddetail.QuickGroupSendDetailMapper;
import cn.aguyao.module.wsapp.service.quickgroupsendaccount.QuickGroupSendAccountService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.QUICK_GROUP_SEND_DETAIL_NOT_EXISTS;

/**
 * 快打群发专属 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuickGroupSendDetailServiceImpl implements QuickGroupSendDetailService {

    @Resource
    private QuickGroupSendDetailMapper quickGroupSendDetailMapper;

    @Resource
    private QuickGroupSendAccountService quickGroupSendAccountService;

    @Resource
    private QuickGroupSendAccountMapper quickGroupSendAccountMapper;

    @Override
    public Long createQuickGroupSendDetail(QuickGroupSendDetailSaveReqVO createReqVO) {
        // 插入
        QuickGroupSendDetailDO quickGroupSendDetail = BeanUtils.toBean(createReqVO, QuickGroupSendDetailDO.class);
        quickGroupSendDetailMapper.insert(quickGroupSendDetail);
        // 返回
        return quickGroupSendDetail.getId();
    }

    @Override
    public void updateQuickGroupSendDetail(QuickGroupSendDetailSaveReqVO updateReqVO) {
        // 校验存在
        validateQuickGroupSendDetailExists(updateReqVO.getId());
        // 更新
        QuickGroupSendDetailDO updateObj = BeanUtils.toBean(updateReqVO, QuickGroupSendDetailDO.class);
        quickGroupSendDetailMapper.updateById(updateObj);
    }

    @Override
    public void deleteQuickGroupSendDetail(Long id) {
        // 校验存在
        validateQuickGroupSendDetailExists(id);
        // 删除
        quickGroupSendDetailMapper.deleteById(id);
    }

    private void validateQuickGroupSendDetailExists(Long id) {
        if (quickGroupSendDetailMapper.selectById(id) == null) {
            throw exception(QUICK_GROUP_SEND_DETAIL_NOT_EXISTS);
        }
    }

    @Override
    public QuickGroupSendDetailDO getQuickGroupSendDetail(Long id) {
        return quickGroupSendDetailMapper.selectById(id);
    }

    @Override
    public PageResult<QuickGroupSendDetailDO> getQuickGroupSendDetailPage(QuickGroupSendDetailPageReqVO pageReqVO) {
        QuickGroupSendAccountDO accountDO = quickGroupSendAccountService.getQuickGroupSendAccount(pageReqVO.getSubTaskId());

        PageResult<QuickGroupSendDetailDO> pageResult = quickGroupSendDetailMapper.selectPage(pageReqVO);
        if (Objects.nonNull(accountDO)) {
            pageResult.getList().forEach(item -> {
                item.setAccount(accountDO.getMobile());
            });
        }

        return pageResult;
    }


    @Override
    public void updateSendStatus(Long detailId, Boolean success, String errMsg, String messageId,Integer messageType) {
        QuickGroupSendDetailDO quickGroupSendDetailDO = this.getQuickGroupSendDetail(detailId);
        if (Objects.isNull(quickGroupSendDetailDO)) {
            // 数据不存在
            return ;
        }
        if (success) {
            switch (messageType) {
                //文字消息
                case QuickGroupSendDetailDO.MSG_TYPE_TEXT:
                    quickGroupSendDetailDO.setTxtMessageId(messageId);
                    break;
                //图片消息
                case QuickGroupSendDetailDO.MSG_TYPE_IMG:
                    quickGroupSendDetailDO.setImgMessageId(messageId);
                    break;
                //语音消息
                case QuickGroupSendDetailDO.MSG_TYPE_VOICE:
                    quickGroupSendDetailDO.setVoiceMessageId(messageId);
                    break;
                //卡片消息
                case QuickGroupSendDetailDO.MSG_TYPE_CARD:
                    quickGroupSendDetailDO.setCardMessageId(messageId);
                    break;
                //图文消息
                case QuickGroupSendDetailDO.MSG_TYPE_IMGTEXT:
                    quickGroupSendDetailDO.setImgtextMessageId(messageId);
                    break;
                //超链消息
                case QuickGroupSendDetailDO.MSG_TYPE_BUTTON:
                    quickGroupSendDetailDO.setButtonMessageId(messageId);
                    break;
                default:
            }
        } else {
            switch (messageType) {
                //文字消息
                case QuickGroupSendDetailDO.MSG_TYPE_TEXT:
                    quickGroupSendDetailDO.setResultInfo((quickGroupSendDetailDO.getResultInfo() == null ? "" : quickGroupSendDetailDO.getResultInfo()) + " | 文字消息发送失败:" + errMsg);
                    break;
                //图片消息
                case QuickGroupSendDetailDO.MSG_TYPE_IMG:
                    quickGroupSendDetailDO.setResultInfo((quickGroupSendDetailDO.getResultInfo() == null ? "" : quickGroupSendDetailDO.getResultInfo()) + " | 图片消息发送失败:" + errMsg);
                    break;
                //语音消息
                case QuickGroupSendDetailDO.MSG_TYPE_VOICE:
                    quickGroupSendDetailDO.setResultInfo((quickGroupSendDetailDO.getResultInfo() == null ? "" : quickGroupSendDetailDO.getResultInfo()) + " | 语音消息发送失败:" + errMsg);
                    break;
                //卡片消息
                case QuickGroupSendDetailDO.MSG_TYPE_CARD:
                    quickGroupSendDetailDO.setResultInfo((quickGroupSendDetailDO.getResultInfo() == null ? "" : quickGroupSendDetailDO.getResultInfo()) + " | 卡片消息发送失败:" + errMsg);
                    break;
                //图文消息
                case QuickGroupSendDetailDO.MSG_TYPE_IMGTEXT:
                    quickGroupSendDetailDO.setResultInfo((quickGroupSendDetailDO.getResultInfo() == null ? "" : quickGroupSendDetailDO.getResultInfo()) + " | 图文消息发送失败:" + errMsg);
                    break;
                //超链消息
                case QuickGroupSendDetailDO.MSG_TYPE_BUTTON:
                    quickGroupSendDetailDO.setResultInfo((quickGroupSendDetailDO.getResultInfo() == null ? "" : quickGroupSendDetailDO.getResultInfo()) + " | 超链消息发送失败:" + errMsg);
                    break;
                default:
            }
            quickGroupSendDetailDO.setExeResult(QuickGroupSendDetailDO.STATUS_FAIL);
        }
        quickGroupSendDetailMapper.updateById(quickGroupSendDetailDO);
        updateQuickGroupSendAccountInfo(quickGroupSendDetailDO.getSubTaskId());
    }

    @Override
    public void updateTaskStatusByMessageId(String messageId) {
        QueryWrapper<QuickGroupSendDetailDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(QuickGroupSendDetailDO::getTxtMessageId, messageId)
                .or()
                .eq(QuickGroupSendDetailDO::getImgMessageId, messageId)
                .or()
                .eq(QuickGroupSendDetailDO::getVoiceMessageId, messageId)
                .or()
                .eq(QuickGroupSendDetailDO::getCardMessageId, messageId)
                .or()
                .eq(QuickGroupSendDetailDO::getImgtextMessageId, messageId)
                .or()
                .eq(QuickGroupSendDetailDO::getTaskId, messageId);
        List<QuickGroupSendDetailDO> doList = quickGroupSendDetailMapper.selectList(queryWrapper);
        if (null != doList && doList.size() > 0) {
            QuickGroupSendDetailDO quickGroupSendDetailDO = doList.get(0);
            quickGroupSendDetailDO.setExeResult(QuickGroupSendDetailDO.STATUS_SUCCESS);
            if (Objects.equals(quickGroupSendDetailDO.getTxtMessageId(), messageId)) {
                quickGroupSendDetailDO.setResultInfo((quickGroupSendDetailDO.getResultInfo() == null ? "" : quickGroupSendDetailDO.getResultInfo()) + " | " + QuickGroupSendDetailDO.TEXT_RESULT_DELAY);
            } else if (Objects.equals(quickGroupSendDetailDO.getImgMessageId(), messageId)) {
                quickGroupSendDetailDO.setResultInfo((quickGroupSendDetailDO.getResultInfo() == null ? "" : quickGroupSendDetailDO.getResultInfo()) + " | " + QuickGroupSendDetailDO.IMG_RESULT_DELAY);
            } else if (Objects.equals(quickGroupSendDetailDO.getVoiceMessageId(), messageId)) {
                quickGroupSendDetailDO.setResultInfo((quickGroupSendDetailDO.getResultInfo() == null ? "" : quickGroupSendDetailDO.getResultInfo()) + " | " + QuickGroupSendDetailDO.VOICE_RESULT_DELAY);
            } else if (Objects.equals(quickGroupSendDetailDO.getCardMessageId(), messageId)) {
                quickGroupSendDetailDO.setResultInfo((quickGroupSendDetailDO.getResultInfo() == null ? "" : quickGroupSendDetailDO.getResultInfo()) + " | " + QuickGroupSendDetailDO.CARD_RESULT_DELAY);
            } else if (Objects.equals(quickGroupSendDetailDO.getImgtextMessageId(), messageId)) {
                quickGroupSendDetailDO.setResultInfo((quickGroupSendDetailDO.getResultInfo() == null ? "" : quickGroupSendDetailDO.getResultInfo()) + " | " + QuickGroupSendDetailDO.IMGTEXT_RESULT_DELAY);
            }else if (Objects.equals(quickGroupSendDetailDO.getButtonMessageId(), messageId)) {
                quickGroupSendDetailDO.setResultInfo((quickGroupSendDetailDO.getResultInfo() == null ? "" : quickGroupSendDetailDO.getResultInfo()) + " | " + QuickGroupSendDetailDO.BUTTON_RESULT_DELAY);
            }
            quickGroupSendDetailMapper.updateById(quickGroupSendDetailDO);
            updateQuickGroupSendAccountInfo(doList.get(0).getSubTaskId());
        }
    }
    @Override
    public void updateQuickGroupSendAccountInfo(Long id) {
        QueryWrapper<QuickGroupSendDetailDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sub_task_id", id);
        queryWrapper.eq("exe_result", 0);
        Long waitingNum =  quickGroupSendDetailMapper.selectCount(queryWrapper);
        if (waitingNum == 0) {
            UpdateWrapper<QuickGroupSendAccountDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", id).set("status", 3).set("completion_time", LocalDateTime.now());
            quickGroupSendAccountMapper.update(null, updateWrapper);
        }
    }

    @Override
    public void batchInsert(List<QuickGroupSendDetailDO> detailList) {
        quickGroupSendDetailMapper.insertBatch(detailList);
    }

    @Override
    public List<Map<Integer, Integer>> countDetailStatusByTaskId(Long taskId) {
        return quickGroupSendDetailMapper.countExeResultByTaskId(taskId);
    }

}