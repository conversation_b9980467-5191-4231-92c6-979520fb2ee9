package cn.aguyao.module.wsapp.framework.datapermission.util;

import cn.aguyao.module.wsapp.framework.datapermission.annotation.NoCreator;
import cn.hutool.core.util.StrUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.mapping.MappedStatement;

import java.lang.reflect.Method;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 方法注解检测器
 * 用于检测 Mapper 方法上的数据权限相关注解
 * 
 * <AUTHOR>
 */
@Log4j2
public class MethodAnnotationDetector {

    /**
     * 缓存方法注解检测结果，避免重复反射
     * key: mappedStatementId, value: 是否有 @NoCreator 注解
     */
    private static final ConcurrentHashMap<String, Boolean> NO_CREATOR_CACHE = new ConcurrentHashMap<>();

    /**
     * 检测指定的 MappedStatement 对应的方法是否标注了 @NoCreator 注解
     * 
     * @param mappedStatement MyBatis 映射语句
     * @return 如果标注了 @NoCreator 注解则返回 true，否则返回 false
     */
    public static boolean hasNoCreatorAnnotation(MappedStatement mappedStatement) {
        String mappedStatementId = mappedStatement.getId();
        
        // 先从缓存中获取
        Boolean cached = NO_CREATOR_CACHE.get(mappedStatementId);
        if (cached != null) {
            return cached;
        }

        // 解析方法信息
        boolean hasAnnotation = detectNoCreatorAnnotation(mappedStatementId);
        
        // 缓存结果
        NO_CREATOR_CACHE.put(mappedStatementId, hasAnnotation);
        
        if (hasAnnotation) {
            log.debug("[hasNoCreatorAnnotation][方法 {} 标注了 @NoCreator 注解，跳过 creator 权限控制]", mappedStatementId);
        }
        
        return hasAnnotation;
    }

    /**
     * 检测方法是否标注了 @NoCreator 注解
     * 
     * @param mappedStatementId MyBatis 映射语句 ID，格式：com.example.mapper.UserMapper.selectById
     * @return 是否标注了 @NoCreator 注解
     */
    private static boolean detectNoCreatorAnnotation(String mappedStatementId) {
        try {
            // 解析 mappedStatementId，格式：包名.类名.方法名
            int lastDotIndex = mappedStatementId.lastIndexOf('.');
            if (lastDotIndex == -1) {
                log.warn("[detectNoCreatorAnnotation][无效的 mappedStatementId: {}]", mappedStatementId);
                return false;
            }

            String className = mappedStatementId.substring(0, lastDotIndex);
            String methodName = mappedStatementId.substring(lastDotIndex + 1);

            // 加载类
            Class<?> mapperClass = Class.forName(className);
            
            // 查找方法（这里简化处理，实际可能需要考虑方法重载）
            Method[] methods = mapperClass.getDeclaredMethods();
            for (Method method : methods) {
                if (method.getName().equals(methodName)) {
                    NoCreator noCreatorAnnotation = method.getAnnotation(NoCreator.class);
                    if (noCreatorAnnotation != null) {
                        String description = StrUtil.isNotBlank(noCreatorAnnotation.value()) 
                                ? noCreatorAnnotation.value() 
                                : "跳过 creator 权限控制";
                        log.info("[detectNoCreatorAnnotation][方法 {}.{} 标注了 @NoCreator 注解: {}]", 
                                className, methodName, description);
                        return true;
                    }
                }
            }
            
            return false;
        } catch (ClassNotFoundException e) {
            log.warn("[detectNoCreatorAnnotation][找不到类: {}]", mappedStatementId, e);
            return false;
        } catch (Exception e) {
            log.error("[detectNoCreatorAnnotation][检测注解失败: {}]", mappedStatementId, e);
            return false;
        }
    }

    /**
     * 清空缓存（用于测试或重新加载）
     */
    public static void clearCache() {
        NO_CREATOR_CACHE.clear();
        log.info("[clearCache][已清空方法注解缓存]");
    }

    /**
     * 获取缓存大小（用于监控）
     */
    public static int getCacheSize() {
        return NO_CREATOR_CACHE.size();
    }

    /**
     * 获取指定方法的缓存状态
     */
    public static Boolean getCachedResult(String mappedStatementId) {
        return NO_CREATOR_CACHE.get(mappedStatementId);
    }
}
