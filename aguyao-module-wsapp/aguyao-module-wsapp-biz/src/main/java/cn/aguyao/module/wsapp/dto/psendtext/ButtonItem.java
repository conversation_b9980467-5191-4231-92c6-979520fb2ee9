package cn.aguyao.module.wsapp.dto.psendtext;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 跟人消息， 发送图文
 */
@Data
public class ButtonItem {

    @JsonProperty("Name")
    private String name;

    @JsonProperty("ButtonParamsJson")
    private ButtonParamsJson buttonParamsJson;

    // 按钮参数内部类
    public static class ButtonParamsJson {
        @JsonProperty("display_text")
        private String displayText;

        @JsonProperty("url")
        private String url;

        @JsonProperty("merchant_url")
        private String merchantUrl;

        @JsonProperty("id")
        private String id;

        // Getters and Setters
        public String getDisplayText() {
            return displayText;
        }

        public void setDisplayText(String displayText) {
            this.displayText = displayText;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getMerchantUrl() {
            return merchantUrl;
        }

        public void setMerchantUrl(String merchantUrl) {
            this.merchantUrl = merchantUrl;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }
    }
}
