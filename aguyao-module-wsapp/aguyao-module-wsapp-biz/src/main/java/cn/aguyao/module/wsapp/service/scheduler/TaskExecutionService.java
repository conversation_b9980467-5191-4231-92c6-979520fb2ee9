package cn.aguyao.module.wsapp.service.scheduler;

import cn.aguyao.module.charging.enums.wsapp.MsgTypeEnum;
import cn.aguyao.module.charging.pojo.ApiResponse;
import cn.aguyao.module.wsapp.controller.admin.groupsendrecord.vo.ButtonVO;
import cn.aguyao.module.wsapp.controller.admin.quickgroupsend.vo.QuickGroupSendSaveReqVO;
import cn.aguyao.module.wsapp.dal.dataobject.quickgroupsend.QuickGroupSendDO;
import cn.aguyao.module.wsapp.dal.dataobject.quickgroupsenddetail.QuickGroupSendDetailDO;
import cn.aguyao.module.wsapp.dal.mysql.quickgroupsend.QuickGroupSendMapper;
import cn.aguyao.module.wsapp.dal.mysql.quickgroupsenddetail.QuickGroupSendDetailMapper;
import cn.aguyao.module.wsapp.dto.psendtext.*;
import cn.aguyao.module.wsapp.service.apiservice.PrivateFunctionService;
import cn.aguyao.module.wsapp.service.cache.SessionInitCacheService;
import cn.aguyao.module.wsapp.service.quickgroupsenddetail.QuickGroupSendDetailService;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Random;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.SEND_CARD_NOT_EMPTY;

/**
 * 任务执行服务
 * 负责具体的任务执行逻辑
 * 
 * <AUTHOR>
 */
@Service
@Log4j2
public class TaskExecutionService {

    @Resource
    private QuickGroupSendDetailMapper quickGroupSendDetailMapper;

    @Resource
    private PrivateFunctionService privateFunctionService;

    @Resource
    private SessionInitCacheService sessionInitCacheService;

    @Resource
    private QuickGroupSendDetailService quickGroupSendDetailService;

    @Resource
    private QuickGroupSendMapper quickGroupSendMapper;
//    @Resource
//    private QuickGroupSendMapper quickGroupSendMapper;
//
//    @Resource
//    private QuickGroupSendAccountMapper quickGroupSendAccountMapper;

    /**
     * 执行任务
     * 
     * @param task 任务详情
     */
    public void executeTask(QuickGroupSendSaveReqVO createReqVO, QuickGroupSendDetailDO task) {
        if (task == null) {
            log.warn("[executeTask][任务为空]");
            return;
        }

        log.info("[executeTask][开始执行任务] taskId={}, sendMobile={}, receiveMobile={}",
                task.getId(), task.getAccount(), task.getMobile());

        try {
            // 更新任务状态为执行中
//            updateTaskStatus(task.getId(), TaskExecutionStatus.EXECUTING);

            // 1. 检查会话是否已初始化
            ApiResponse apiResponse = checkAndInitSession(task.getAccount(), task.getMobile());
            if (apiResponse == null ) {
                log.error("[executeTask][会话初始化失败] taskId={}", task.getId());
                updateTaskStatus(task.getId(), TaskExecutionStatus.FAILED, "会话初始化失败");
                return;
            } else if (!apiResponse.getSuccess()){
                log.error("[executeTask][会话初始化失败] taskId={}", task.getId());
                updateTaskStatus(task.getId(), TaskExecutionStatus.FAILED, apiResponse.getErrMsg());
                return;
            }

            // 2. 执行具体的业务逻辑（发送消息等）
            boolean success = executeBusinessLogic(createReqVO, task);

            if (success) {
                log.info("[executeTask][任务执行成功] taskId={}", task.getId());
//                updateTaskStatus(task.getId(), TaskExecutionStatus.EXECUTING);
            } else {
                log.error("[executeTask][任务执行失败] taskId={}", task.getId());
                updateTaskStatus(task.getId(), TaskExecutionStatus.FAILED, "业务逻辑执行失败");
            }

        } catch (Exception e) {
            log.error("[executeTask][任务执行异常] taskId={}", task.getId(), e);
            updateTaskStatus(task.getId(), TaskExecutionStatus.FAILED, "执行异常: " + e.getMessage());
        }
    }

    /**
     * 检查并初始化会话
     * 
     * @param sendMobile 发送方手机号
     * @param receiveMobile 接收方手机号
     * @return 是否初始化成功
     */
    private ApiResponse checkAndInitSession(String sendMobile, String receiveMobile) {
        try {
            // 检查缓存中是否已有成功的初始化记录
            if (sessionInitCacheService.isInitSuccess(sendMobile, receiveMobile)) {
                log.debug("[checkAndInitSession][会话已初始化] sendMobile={}, receiveMobile={}", 
                        sendMobile, receiveMobile);
                return null;
            }

            // 执行会话初始化
            log.info("[checkAndInitSession][开始初始化会话] sendMobile={}, receiveMobile={}", 
                    sendMobile, receiveMobile);
            
            return privateFunctionService.initSession(sendMobile, receiveMobile);

        } catch (Exception e) {
            log.error("[checkAndInitSession][会话初始化异常] sendMobile={}, receiveMobile={}", 
                    sendMobile, receiveMobile, e);
            return null;
        }
    }

    /**
     * 执行具体的业务逻辑
     * 
     * @param task 任务
     * @return 是否执行成功
     */
    private boolean executeBusinessLogic(QuickGroupSendSaveReqVO createReqVO, QuickGroupSendDetailDO task) {
        try {
            createReqVO.getMessageContents().forEach(messageContent -> {
                if (MsgTypeEnum.TEXT.getType().equals(messageContent.getType())) {
                    // 发送文字
                    sendTextMessage(messageContent.getContent(), task);
                } else if (MsgTypeEnum.IMAGE.getType().equals(messageContent.getType())) {
                    // 发送图片
                    sendImageMessage(messageContent.getContent(), task);
                } else if (MsgTypeEnum.VOICE.getType().equals(messageContent.getType())) {
                    // 发送语音
                    sendVoiceMessage(messageContent.getContent(), task);
                } else if (MsgTypeEnum.CARD.getType().equals(messageContent.getType())) {
                    // 发送名片
                    sendCardMessage(messageContent.getContent(), task);
                } else if (MsgTypeEnum.IMG_TEXT.getType().equals(messageContent.getType())) {
                    // 发送图文
                    sendImgTextMessage(messageContent.getContent(), task);
                } else if (MsgTypeEnum.BUTTON.getType().equals(messageContent.getType())) {
                    // 发送超链
                    sendCardMessage(messageContent.getContent(), task);
                }
                /*
                 * ...
                 */
            });

        } catch (Exception e) {
            log.error("[executeBusinessLogic][业务逻辑执行异常] taskId={}", task.getId(), e);
            return false;
        }
        return true;
    }

    /**
     * 发送文本消息
     */
    private void sendTextMessage(String content, QuickGroupSendDetailDO task) {
        log.info("[sendTextMessage][发送文本消息] taskId={}, content={}", 
                task.getId(), content);
        
        // 调用实际的发送文本消息API
        SendTextReq sendTextReq = SendTextReq.builder()
                .toUserId(task.getMobile())
                .isGroup(false)
                .isFakeMsg(false)
                .stanzaId(IdUtil.fastSimpleUUID())
                .participant(task.getMobile() + "@s.whatsapp.net")
                .content(content)
                .contextInfo(SendTextReq.ContextInfoEnum.CONTACT_SEARCH.getType())
                .build();
        privateFunctionService.callSendTextApi(task.getId(), task.getAccount(), sendTextReq);
    }

    /**
     * 发送图片消息
     */
    private void sendImageMessage(String content, QuickGroupSendDetailDO task) {
        log.info("[sendImageMessage][发送图片消息] taskId={}, imageUrl={}", 
                task.getId(), content);

        // 调用实际的发送图片消息API
        SendImgReq sendImgReq = SendImgReq.builder()
                .toUserId(task.getMobile())
                .isGroup(false)
                .isFakeMsg(false)
                .ImageUrl(content)
                .content(content)
                .contextInfo(SendTextReq.ContextInfoEnum.CONTACT_SEARCH.getType())
                .build();
        privateFunctionService.callSendImgApi(task.getId(), task.getAccount(), sendImgReq);
    }
    /**
     * 发送语音消息
     */
    private void sendVoiceMessage(String content, QuickGroupSendDetailDO task) {
        log.info("[sendImageMessage][发送语音消息] taskId={}, voiceUrl={}",
                task.getId(), content);

        // 调用实际的发送语音消息API
        SendVoiceReq sendVoiceReq = SendVoiceReq.builder()
                .toUserId(task.getMobile())
                .isGroup(false)
                .isFakeMsg(false)
                .VoiceUrl(content)
                .contextInfo(SendTextReq.ContextInfoEnum.CONTACT_SEARCH.getType())
                .build();
        privateFunctionService.callSendVoiceApi(task.getId(), task.getAccount(), sendVoiceReq);
    }
    /**
     * 发送名片消息
     */
    private void sendCardMessage(String content, QuickGroupSendDetailDO task) {
        log.info("[sendImageMessage][发送卡片消息] taskId={}, content={}",
                task.getId(), content);
        if (StrUtil.isBlank(content)) {
            throw exception(SEND_CARD_NOT_EMPTY);
        }

        String[] cards = content.split("\n");
        Random random = new Random();
        int randomIndex = random.nextInt(cards.length);
        // 返回随机选中的元素
        String card = cards[randomIndex];
        String[] cardTelAndName = card.split("#");
        if (cardTelAndName.length == 2) {
            // 调用实际的发送卡片消息API
            SendCardReq sendCardReq = SendCardReq.builder()
                    .toUserId(task.getMobile())
                    .isGroup(false)
                    .isFakeMsg(false)
                    .VCardTel(cardTelAndName[1])
                    .VCardName(cardTelAndName[0])
                    .contextInfo(SendTextReq.ContextInfoEnum.CONTACT_SEARCH.getType())
                    .build();
            privateFunctionService.callSendCardApi(task.getId(), task.getAccount(), sendCardReq);
        }
    }

    /**
     * 发送图文消息
     */
    private void sendImgTextMessage(String content, QuickGroupSendDetailDO task) {
        log.info("[sendImageMessage][发送图文消息] taskId={}, content={}",
                task.getId(), content);

        QuickGroupSendDO quickGroupSendDO = quickGroupSendMapper.selectById(task.getTaskId());
        List<ButtonVO> buttonVoList = quickGroupSendDO.getButtonsList();
        List<ButtonItem> buttonItemList = new ArrayList<>();
        for (int i = 0; i < buttonVoList.size(); i++ ) {
            ButtonVO buttonVO = buttonVoList.get(i);
            ButtonItem buttonItem = new ButtonItem();
            ButtonItem.ButtonParamsJson buttonParams = new ButtonItem.ButtonParamsJson();
            buttonParams.setDisplayText(buttonVO.getText());
            buttonParams.setUrl(buttonVO.getUrl());
            buttonParams.setMerchantUrl(buttonVO.getUrl());
            buttonParams.setId("button-" + i);
            buttonItem.setButtonParamsJson(buttonParams);
            buttonItemList.add(buttonItem);
        }
        // 调用实际的发送卡片消息API
        SendImgTextReq sendImgTextReq = SendImgTextReq.builder()
                .toUserId(task.getMobile())
                .isGroup(false)
                .title(quickGroupSendDO.getTitle())
                .body(quickGroupSendDO.getBody())
                .footer(quickGroupSendDO.getFooter())
                .imageUrl(quickGroupSendDO.getImageUrl())
                .buttons(buttonItemList)
                .contextInfo(SendTextReq.ContextInfoEnum.CONTACT_SEARCH.getType())
                .build();
        privateFunctionService.callSendImgTextApi(task.getId(), task.getAccount(), sendImgTextReq);
    }

    /**
     * 发送群组消息
     */
    private void sendGroupMessage(String content, QuickGroupSendDetailDO task) {
        log.info("[sendGroupMessage][发送群组消息] taskId={}, groupId={}", 
                task.getId(), content);
        
        // TODO: 调用实际的发送群组消息API
        // return privateFunctionService.sendGroupMessage(task.getSendMobile(), task.getAcctGroup(), task.getContent());
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(Long taskId, TaskExecutionStatus status) {
        updateTaskStatus(taskId, status, null);
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(Long taskId, TaskExecutionStatus status, String errorMessage) {
        try {
            QuickGroupSendDetailDO updateTask = new QuickGroupSendDetailDO();
            updateTask.setId(taskId);
            updateTask.setExeResult(status.getCode());
            QueryWrapper<QuickGroupSendDetailDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("id", taskId);
            List<QuickGroupSendDetailDO> doList =  quickGroupSendDetailMapper.selectList(queryWrapper);
            if (Objects.nonNull(doList)) {
                quickGroupSendDetailService.updateQuickGroupSendAccountInfo(doList.get(0).getSubTaskId());
            }
            if (errorMessage != null) {
                updateTask.setResultInfo(errorMessage);
            }

            quickGroupSendDetailMapper.updateById(updateTask);
            
            log.debug("[updateTaskStatus][更新任务状态] taskId={}, status={}, errorMessage={}", 
                    taskId, status, errorMessage);

        } catch (Exception e) {
            log.error("[updateTaskStatus][更新任务状态失败] taskId={}, status={}", taskId, status, e);
        }
    }

    /**
     * 任务类型枚举
     */
    private enum TaskType {
        SEND_TEXT_MESSAGE,
        SEND_IMAGE_MESSAGE,
        SEND_GROUP_MESSAGE
    }

    /**
     * 任务执行状态枚举
     */
    public enum TaskExecutionStatus {
        PENDING(0, "待执行"),
        SUCCESS(1, "执行成功"),
        FAILED(2, "执行失败");

        private final Integer code;
        private final String description;

        TaskExecutionStatus(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
