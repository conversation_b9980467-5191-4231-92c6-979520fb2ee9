package cn.aguyao.module.wsapp.config;

import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * WhatsApp 模块 RabbitMQ 配置类
 * 
 * <AUTHOR>
 */
@Configuration
public class WSAppRabbitMQConfig {

    // ==================== 登录相关队列配置 ====================
    
    /**
     * 登录任务队列名称
     */
    public static final String LOGIN_QUEUE = "wsapp-queue-name";
    
    /**
     * 登录任务交换机名称
     */
//    public static final String LOGIN_EXCHANGE = "wsapp.login.exchange";
    public static final String LOGIN_EXCHANGE = "wsapp-event-direct";
    
    /**
     * 登录任务路由键
     */
//    public static final String LOGIN_ROUTING_KEY = "wsapp.login.routing";
    public static final String LOGIN_ROUTING_KEY = "wsapp";

    /**
     * 登录任务队列
     */
    @Bean
    public Queue loginQueue() {
        return QueueBuilder.durable(LOGIN_QUEUE).build();
    }

    /**
     * 登录任务直连交换机
     */
    @Bean
    public DirectExchange loginExchange() {
        return new DirectExchange(LOGIN_EXCHANGE);
    }

    /**
     * 绑定登录队列到交换机
     */
    @Bean
    public Binding loginBinding() {
        return BindingBuilder.bind(loginQueue()).to(loginExchange()).with(LOGIN_ROUTING_KEY);
    }

}
