package cn.aguyao.module.wsapp.service.impdata;


import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.module.wsapp.controller.admin.baseaccount.vo.AccountPageReqVO;
import cn.aguyao.module.wsapp.controller.admin.impdata.vo.ImpdataPageReqVO;
import cn.aguyao.module.wsapp.controller.admin.impdata.vo.ImpdataReqVO;
import cn.aguyao.module.wsapp.dal.dataobject.account.AccountDO;
import cn.aguyao.module.wsapp.dal.dataobject.impdata.DataUploadDO;
import cn.aguyao.module.wsapp.dal.dataobject.impdata.ImpdataDO;

import java.util.List;

/**
 * 数据导入 Service 接口
 *
 */
public interface ImpdataService {

    /**
     * 数据导入
     *
     * @param impdataReqVO 数据导入
     *
     */
    String impdata(ImpdataReqVO impdataReqVO);

    /**
     * 数据列表分页
     *
     * @param pageReqVO 分页查询
     * @return 数据分页
     */
    PageResult<ImpdataDO> getImpdataPage(ImpdataPageReqVO pageReqVO);

    String getData(String base64String);

    /**
     * 删除分
     *
     * @param id
     */
    void deleteGroup(Long id);

    /**
     * 批量删除
     * @param ids id
     */
    void batchDelete(List<Long> ids);

    void updateVersion(int type, String version);

    DataUploadDO getVersion();
}