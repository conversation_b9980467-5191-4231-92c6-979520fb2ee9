package cn.aguyao.module.wsapp.service.apiservice;

import cn.aguyao.module.charging.enums.wsapp.ReqUriEnum;
import cn.aguyao.module.system.api.dict.DictDataApi;
import cn.aguyao.module.system.api.dict.dto.DictDataRespDTO;
import cn.aguyao.module.wsapp.controller.admin.baseaccount.vo.AccountLoginVO;
import cn.aguyao.module.wsapp.dal.dataobject.ipmanager.IpManagerDO;
import cn.aguyao.module.wsapp.service.baseaccount.BaseAccountService;
import cn.aguyao.module.wsapp.service.ipmanager.IpManagerService;
import cn.aguyao.module.wsapp.util.DeviceUtil;
import cn.aguyao.module.wsapp.util.LoginAppVersion;
import cn.aguyao.module.wsapp.util.LoginUserAgent;
import cn.aguyao.module.wsapp.util.WSVersion;
import cn.hutool.core.util.ObjUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.log4j.Log4j2;
import okhttp3.*;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * 登录相关，比如：安卓登录、苹果登录等
 * <AUTHOR> aguyao
 */
@Log4j2
@Service
public class LoginAsyncService {


    @Resource
    private OkHttpClient okHttpClient4Login;

    @Resource
    private IpManagerService ipManagerService;


    @Lazy
    @Resource
    private BaseAccountService baseAccountService;

    @Lazy
    @Resource
    private DictDataApi dictDataApi;
    /**
     * 调用登录接口
     * @param accountLoginVO
     */
    @Async
    public void callLoginApi(AccountLoginVO accountLoginVO) {
        // 设置Socks5属性
        String ip = "socks5://bajiequnfa_1:<EMAIL>:2079";//代理IP，默认动态
        //如果是静态ip，从ip管理组下随机取一条静态ip
        if (accountLoginVO.getProxyType() == 2) {
            List<IpManagerDO> ipManagerDOList= ipManagerService.getIpByIpGroupId(accountLoginVO.getIpGroupId());
            if (!Objects.isNull(ipManagerDOList) && !ipManagerDOList.isEmpty()) {
                IpManagerDO ipManagerDO = ipManagerDOList.get(0);
                ip = String.format("socks5://%s:%s@%s:%d",ipManagerDO.getAccount(), ipManagerDO.getPassword(), ipManagerDO.getIp(), ipManagerDO.getPort());
            }
        }
        accountLoginVO.setIp(ip);
        RequestBody body = RequestBody.create(accountLoginVO.getMediaType(), createLoginJson(accountLoginVO));
        Request request = new Request.Builder()
                .url(accountLoginVO.getUrl())
                .method("POST", body)
                .build();
        try {
            try (Response response = okHttpClient4Login.newCall(request).execute()) {
                ResponseBody result = response.body();
                if (!Objects.isNull(result)) {
                    String resultJsonStr = result.string();
                    log.info("======= 手机号：{}，  登录结果：{}", accountLoginVO.getAccount(), resultJsonStr);
                    accountLoginVO.setResultJsonStr(resultJsonStr);
                    // 保存账号信息
                    baseAccountService.saveAccountInfo(accountLoginVO);
                } else {
                    log.info("===================结果为空， 手机号：{}", accountLoginVO.getAccount());
                }

            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    //根据手机号匹配
    public String getLangCode(String mobile) {
        //依次根据手机号前3,2,1位置去匹配区域字典
        DictDataRespDTO dictData = dictDataApi.getDictData("wsapp_country_code",mobile.substring(0,3));
        if (ObjUtil.isNull(dictData)) {
            dictData = dictDataApi.getDictData("wsapp_country_code",mobile.substring(0,2));
        }
        if (ObjUtil.isNull(dictData)) {
            dictData = dictDataApi.getDictData("wsapp_country_code",mobile.substring(0,1));
        }
        if (ObjUtil.isNull(dictData) || ObjUtil.isNull(dictData.getRemark())) {
            return "US";
        }
        return dictData.getRemark();
    }
    private String createLoginJson(AccountLoginVO accountLoginVO) {
        try {
            // 创建ObjectMapper实例
            ObjectMapper mapper = new ObjectMapper();

            // 创建根节点
            ObjectNode rootNode = mapper.createObjectNode();

            // 创建CgiRequest节点
            ObjectNode cgiRequestNode = mapper.createObjectNode();
            rootNode.set("CgiRequest", cgiRequestNode);

            // 设置CgiRequest的基本属性
            cgiRequestNode.put("User", accountLoginVO.getAccountFullStr());
            cgiRequestNode.put("RegInfo", "wsapp");
//            cgiRequestNode.put("RegInfo", "user2");

            // 创建ClientPayload节点
            ObjectNode clientPayloadNode = mapper.createObjectNode();
            cgiRequestNode.set("ClientPayload", clientPayloadNode);

            // 设置ClientPayload的基本属性
            clientPayloadNode.put("username", Long.parseLong(accountLoginVO.getAccount())); //这里注意，手机号必须是数字类型，不能是字符串
//            clientPayloadNode.put("pushName", accountLoginVO.getAccount());
            clientPayloadNode.put("passive", false);
            clientPayloadNode.put("shortConnect", true);
            clientPayloadNode.put("connectType", 1);
            clientPayloadNode.put("connectReason", 1);
            clientPayloadNode.put("connectAttemptCount", 0);
            clientPayloadNode.put("device", 0);

            // 创建userAgent节点
            ObjectNode userAgentNode = mapper.createObjectNode();
            LoginUserAgent userAgent = new LoginUserAgent();
            int platform = 10;//默认安卓商务
            if (accountLoginVO.getSystemType() == 1) {
                if (accountLoginVO.getVersionType() == 1) {
                    platform = Integer.parseInt(ReqUriEnum.PLATFORM_ANDROID.getUri()); //安卓个人
                } else {
                    platform = Integer.parseInt(ReqUriEnum.PLATFORM_ANDROID_BUSINESS.getUri()); //安卓商务
                }
            } else {
                if (accountLoginVO.getVersionType() == 1) {
                    platform = Integer.parseInt(ReqUriEnum.PLATFORM_IOS.getUri()); //苹果个人
                } else {
                    platform = Integer.parseInt(ReqUriEnum.PLATFORM_IOS_BUSINESS.getUri()); //苹果商务
                }
            }

            WSVersion wsVersion = WSVersion.getByPlatform(platform);
            userAgent.platform = platform;
            userAgent.appVersion = new LoginAppVersion(wsVersion);
            userAgent.mcc = "000";
            userAgent.mnc = "000";
            // 安卓
            if (accountLoginVO.getSystemType() == 1) {
                DeviceUtil.AndroidDevice device = DeviceUtil.getAndroidDevice();
                userAgent.osVersion = DeviceUtil.getAndroidOsVersion();
                userAgent.manufacturer = device.brand;
                userAgent.device = device.device;
                userAgent.osBuildNumber = DeviceUtil.genOsBuildNumber();
                userAgent.phoneId = UUID.randomUUID().toString();
                userAgent.deviceModelType = device.deviceModel;
                userAgent.localeCountryIso31661Alpha2 = getLangCode(accountLoginVO.getAccount());
                userAgent.localeLanguageIso6391 = DeviceUtil.getLangCode(getLangCode(accountLoginVO.getAccount()));
                String pickName = DeviceUtil.pickName(accountLoginVO.getIndex());
                accountLoginVO.setNickName(pickName);
                clientPayloadNode.put("pushName", pickName);
            }
            // IOS
            if (accountLoginVO.getSystemType() == 2) {
                userAgent.osVersion = DeviceUtil.getIOSBuildVersionList();
                userAgent.manufacturer = "Apple";
                userAgent.device = DeviceUtil.getIOSDeviceName();
                userAgent.osBuildNumber = DeviceUtil.iosBuildNumberMap.get(userAgent.osVersion);
                userAgent.phoneId = UUID.randomUUID().toString().toLowerCase();
                userAgent.deviceModelType = DeviceUtil.iosDeviceModelTypeMap.get(userAgent.device);
                userAgent.localeCountryIso31661Alpha2 = getLangCode(accountLoginVO.getAccount());
                userAgent.localeLanguageIso6391 = DeviceUtil.getLangCode(getLangCode(accountLoginVO.getAccount()));
                String pickName = DeviceUtil.pickName(accountLoginVO.getIndex());
                accountLoginVO.setNickName(pickName);
                clientPayloadNode.put("pushName", pickName);
            }
            clientPayloadNode.set("userAgent", mapper.valueToTree(userAgent));
            // 创建dnsSource节点
            ObjectNode dnsSourceNode = mapper.createObjectNode();
            clientPayloadNode.set("dnsSource", dnsSourceNode);

            // 设置dnsSource的属性
            dnsSourceNode.put("dnsMethod", 0);

            cgiRequestNode.put("Socks5", accountLoginVO.getIp());

            // 生成格式化的JSON字符串
            String jsonString = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(rootNode);
            log.info("===================生成格式化的JSON字符串 jsonString：{}", jsonString);
            return jsonString;
        } catch (IOException e) {
//            e.printStackTrace();
            log.error("===================生成格式化的JSON字符串 jsonString：{}", e.getMessage());
        }
        return "";
    }

    /**
     * 获取登录二维码
     */
    public String callLoginQrcodeApi(AccountLoginVO accountLoginVO) {
        // 设置Socks5属性
        String loginQrStr = "";
        String ip = "socks5://bajiequnfa_1:<EMAIL>:2079";//代理IP，默认动态
        //如果是静态ip，从ip管理组下随机取一条静态ip
        if (accountLoginVO.getProxyType() == 2) {
            List<IpManagerDO> ipManagerDOList= ipManagerService.getIpByIpGroupId(accountLoginVO.getIpGroupId());
            if (!Objects.isNull(ipManagerDOList) && !ipManagerDOList.isEmpty()) {
                IpManagerDO ipManagerDO = ipManagerDOList.get(0);
                ip = String.format("socks5://%s:%s@%s:%d",ipManagerDO.getAccount(), ipManagerDO.getPassword(), ipManagerDO.getIp(), ipManagerDO.getPort());
            }
        }
        accountLoginVO.setIp(ip);
        RequestBody body = RequestBody.create(accountLoginVO.getMediaType(), createLoginQrcodeJson(accountLoginVO));
        Request request = new Request.Builder()
                .url(accountLoginVO.getUrl())
                .method("POST", body)
                .build();
        try {
            try (Response response = okHttpClient4Login.newCall(request).execute()) {
                ResponseBody result = response.body();
                if (!Objects.isNull(result)) {
                    String resultJsonStr = result.string();
                    log.info("======= 获取登录二维码结果：{}",resultJsonStr);
                    // 直接将 JSON 转为 Map，无需定义实体类
                    ObjectMapper mapper = new ObjectMapper();
                    Map<String, Object> data = mapper.readValue(resultJsonStr, Map.class);
                    // 获取 Ret
                    Map<String, Object> cgiBaseResponse = (Map<String, Object>) data.get("CgiBaseResponse");
                    int ret = ((Number) cgiBaseResponse.get("Ret")).intValue();
                    if (ret == 0) {
                        Map<String, Object> responseData = (Map<String, Object>) data.get("ResponseData");
                        loginQrStr = (String) responseData.get("QRCode");
                    }
                    System.out.println("二维码: " + loginQrStr);
                } else {
                    log.info("==================获取登录二维码结果为空");
                }

            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return loginQrStr;
    }

    private String createLoginQrcodeJson(AccountLoginVO accountLoginVO) {
        try {
            // 创建ObjectMapper实例
            ObjectMapper mapper = new ObjectMapper();

            // 创建根节点
            ObjectNode rootNode = mapper.createObjectNode();

            // 创建CgiRequest节点
            ObjectNode cgiRequestNode = mapper.createObjectNode();
            rootNode.set("CgiRequest", cgiRequestNode);

            // 设置CgiRequest的基本属性
            cgiRequestNode.put("appVersion", "2.3000.**********");
            cgiRequestNode.put("PhoneNum", "");
            cgiRequestNode.put("RegInfo", "wsapp");
            cgiRequestNode.put("Country", "");

            int platform = 10;//默认安卓商务
            if (accountLoginVO.getSystemType() == 1) {
                if (accountLoginVO.getVersionType() == 1) {
                    platform = Integer.parseInt(ReqUriEnum.PLATFORM_ANDROID.getUri()); //安卓个人
                } else {
                    platform = Integer.parseInt(ReqUriEnum.PLATFORM_ANDROID_BUSINESS.getUri()); //安卓商务
                }
            } else {
                if (accountLoginVO.getVersionType() == 1) {
                    platform = Integer.parseInt(ReqUriEnum.PLATFORM_IOS.getUri()); //苹果个人
                } else {
                    platform = Integer.parseInt(ReqUriEnum.PLATFORM_IOS_BUSINESS.getUri()); //苹果商务
                }
            }
            cgiRequestNode.put("platform", platform);
            cgiRequestNode.put("Socks5", accountLoginVO.getIp());

            // 生成格式化的JSON字符串
            String jsonString = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(rootNode);
            log.info("===================生成格式化的JSON字符串 jsonString：{}", jsonString);
            return jsonString;
        } catch (IOException e) {
//            e.printStackTrace();
            log.error("===================生成格式化的JSON字符串 jsonString：{}", e.getMessage());
        }
        return "";
    }
}