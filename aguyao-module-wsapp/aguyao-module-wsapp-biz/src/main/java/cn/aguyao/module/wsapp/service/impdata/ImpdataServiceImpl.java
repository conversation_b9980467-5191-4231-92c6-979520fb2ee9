package cn.aguyao.module.wsapp.service.impdata;

import cn.aguyao.framework.common.exception.ErrorCode;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.module.wsapp.controller.admin.impdata.vo.ImpdataPageReqVO;
import cn.aguyao.module.wsapp.controller.admin.impdata.vo.ImpdataReqVO;
import cn.aguyao.module.wsapp.dal.dataobject.impdata.DataUploadDO;
import cn.aguyao.module.wsapp.dal.dataobject.impdata.ImpdataDO;
import cn.aguyao.module.wsapp.dal.mysql.impdata.DataUploadMapper;
import cn.aguyao.module.wsapp.dal.mysql.impdata.ImpdataMapper;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.log4j.Log4j2;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.jce.spec.IESParameterSpec;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import javax.annotation.Resource;
import javax.crypto.Cipher;
import java.security.*;
import java.security.spec.ECGenParameterSpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 账号管理 Service 实现类
 *
 * <AUTHOR>
 */
@Log4j2
@Service
@Validated
public class ImpdataServiceImpl implements ImpdataService {

    @Resource
    private ImpdataMapper impdataMapper;

    @Resource
    private DataUploadMapper dataUploadMapper;

    static {
        // 注册 BouncyCastle 加密提供者
        Security.addProvider(new BouncyCastleProvider());
    }
    @Override
    public String impdata(ImpdataReqVO impdataReqVO) {
        long startTime = System.currentTimeMillis();
        if (StrUtil.isBlank(impdataReqVO.getData())) {
            throw exception(new ErrorCode(1_001, "导入的数据不能为空"));
        }

        String[] datas = impdataReqVO.getData().split("\n");
        List<ImpdataDO> impdataDOList = Arrays.stream(datas == null ? new String[0] : datas)
                .filter(data -> data != null && !data.trim().isEmpty()) // 过滤空值
                .map(data -> {
                    ImpdataDO doObj = new ImpdataDO();
                    doObj.setData(data);
                    return doObj;
                }) // 映射为ImpdataDO对象
                .collect(Collectors.toList()); // 收集为List

        // 1. 提取待插入的所有data值
        List<String> allDataList = impdataDOList.stream()
                .map(ImpdataDO::getData)
                .filter(Objects::nonNull) // 过滤data为null的记录
                .collect(Collectors.toList());

        // 2. 查询数据库中已存在的data值
        List<String> existingDataList = impdataMapper.selectExistingData(allDataList);
        Set<String> existingDataSet = new HashSet<>(existingDataList); // 转为Set提高查询效率

        // 3，将未新增记录的data用换行符拼接成单个String
        String uninsertedDataStr = impdataDOList.stream()
                // 过滤出已存在的记录（data非空且在数据库中存在）
                .filter(doObj -> doObj.getData() != null && existingDataSet.contains(doObj.getData()))
                // 提取data属性
                .map(ImpdataDO::getData)
                // 用换行符拼接所有字符串（若为空列表则返回空字符串）
                .collect(Collectors.joining("\n"));


        // 4. 筛选出需要插入的新记录（不存在的）
        List<ImpdataDO> toInsertList = impdataDOList.stream()
                .filter(doObj -> {
                    String data = doObj.getData();
                    return data != null && !existingDataSet.contains(data);
                })
                .collect(Collectors.toList());

        // 5. 批量插入新记录
        if (!toInsertList.isEmpty()) {
            impdataMapper.insertBatch(toInsertList,200);
        }
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        double seconds = duration / 1000.0;
        System.out.printf("登录请求执行时间: %.3f 秒%n", seconds);
        // 6. 返回未新增的记录（已存在的）
        return uninsertedDataStr;
    }

    @Override
    public PageResult<ImpdataDO> getImpdataPage(ImpdataPageReqVO pageReqVO) {
        return impdataMapper.selectPage(pageReqVO);
    }

    @Override
    public String getData(String base64Str) {

        // 预处理：移除所有空白字符（包括空格、制表符、换行符等）
        base64Str = base64Str.replaceAll("-","+").replaceAll("_","/");
        String encryptedData = "";
        // 1，base64转字节数组
        byte[] byteArray = base64ToByteArray(base64Str);

        byte [] dataPart; // 前面的部分（除了最后 12 个字节）
        byte [] keyPart; // 最后 12 个字节

        int length = byteArray.length;
        if (length <= 12) {
            throw exception(new ErrorCode(1_002, "请求参数错误"));
        } else {
            int splitIndex = length - 12; // 后 12 位的起始索引
            dataPart = Arrays.copyOfRange (byteArray, 0, splitIndex); // 0 到 splitIndex-1
            keyPart = Arrays.copyOfRange (byteArray, splitIndex, length); //splitIndex 到末尾（共 12 个）
        }


        // 2，解密：使用相同密钥异或
        byte[] decrypted = xor(dataPart, keyPart);
        String decryptedStr = new String(decrypted);
        System.out.println("解密后：" + decryptedStr);
        // 3，去数据库匹配
        LambdaQueryWrapper<ImpdataDO> lambdaWrapper = new LambdaQueryWrapper<>();
        lambdaWrapper.eq(ImpdataDO::getData, decryptedStr);
        ImpdataDO impdataDO = impdataMapper.selectOne(lambdaWrapper);
        if (impdataDO == null) {
            throw exception(new ErrorCode(1_003, "无授权信息"));
        } else {
            long timestampSeconds2 = impdataDO.getCreateTime()
                    .atZone(ZoneId.of("Asia/Shanghai")) // 绑定北京时间时区
                    .toEpochSecond() + 31536000L; // 直接获取时间戳（内部已自动转为UTC基准）
            String originalData = impdataDO.getData() + "," + timestampSeconds2;
            System.out.println("原始数据: " + originalData);
            byte [] resDataPart = xor(originalData.getBytes(), keyPart);
            byte[] result = new byte[resDataPart.length + keyPart.length];
            // 复制数组a到结果数组的开头
            System.arraycopy(resDataPart, 0, result, 0, resDataPart.length);
            // 复制数组b到结果数组中a的后面
            System.arraycopy(keyPart, 0, result, resDataPart.length, keyPart.length);
            encryptedData = Base64.getEncoder().encodeToString(result);
            System.out.println("加密后返回: " + Base64.getEncoder().encodeToString(result));
            // 加密
//            // 4，生成密钥对
//            try {
//
//                // 1. 必须先注册BouncyCastle提供者（关键步骤）
//                Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
//
//                // 2. 生成示例密钥对（实际使用中应替换为预配置的密钥）
//                String publicKeyBase64 = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAESkZRUbiJIgOPizDGOckp9pQXck+IXv6z6Nb5zd+gGQOsGzEhQnAKNdKjRXcmsLkvvF8xth1jZwtWcAy/vgG1Uw==";
////                String privateKeyBase64 = "MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQgG7fW32MBWZmeuh4Tfo5zaz+58+gVtxbDC5M40TWC2KugCgYIKoZIzj0DAQehRANCAARKRlFRuIkiA4+LMMY5ySn2lBdyT4he/rPo1vnN36AZA6wbMSFCcAo10qNFdyawuS+8XzG2HWNnC1ZwDL++AbVT";
//
//                // 3. 正确解析公钥（使用X509EncodedKeySpec）
//                PublicKey publicKey = decodePublicKey(publicKeyBase64);
//                // 4. 正确解析私钥（使用PKCS8EncodedKeySpec）
////                PrivateKey privateKey = decodePrivateKey(privateKeyBase64);
//
//                IESParameterSpec iesParams = new IESParameterSpec(
//                        "derivation".getBytes(), "encoding".getBytes(), 128
//                );
//
//                // 加密
//                Cipher cipher = Cipher.getInstance("ECIES", "BC");
//                cipher.init(Cipher.ENCRYPT_MODE, publicKey, iesParams);
//                byte[] encrypted = cipher.doFinal(originalData.getBytes());
//                encryptedData = Base64.getEncoder().encodeToString(encrypted);
//                System.out.println("加密后: " + Base64.getEncoder().encodeToString(encrypted));
//                // 解密
////                cipher.init(Cipher.DECRYPT_MODE, privateKey, iesParams);
////                byte[] decrypteds = cipher.doFinal(encrypted);
////                System.out.println("解密结果: " + new String(decrypteds));
//            } catch (Exception e) {
//                throw new RuntimeException(e);
//            }

        }
        return encryptedData;
    }
    /**
     * 将Base64字符串转换为字节数组
     * @param base64Str Base64编码的字符串
     * @return 转换后的字节数组
     */
    public static byte[] base64ToByteArray(String base64Str) {
        if (base64Str == null || base64Str.isEmpty()) {
            throw new IllegalArgumentException("Base64字符串不能为空");
        }
        // 使用Base64解码器解码
        return Base64.getDecoder().decode(base64Str);
    }
    // 修复Base64填充问题的方法
    private static String fixBase64Padding(String base64) {
        int padding = 4 - (base64.length() % 4);
        if (padding == 4) {
            return base64; // 已经是4的倍数，无需修复
        }
        // 添加必要的填充符
        StringBuilder sb = new StringBuilder(base64);
        for (int i = 0; i < padding; i++) {
            sb.append('=');
        }
        return sb.toString();
    }
    /**
     * 异或加密/解密（加密和解密使用相同逻辑）
     * @param data 原始数据（字节数组）
     * @param key 密钥（字节数组）
     * @return 处理后的数据
     */
    public static byte[] xor(byte[] data, byte[] key) {
        if (data == null || key == null || key.length == 0) {
            throw new IllegalArgumentException("数据或密钥不能为空");
        }

        byte[] result = new byte[data.length];
        // 遍历数据，与密钥进行异或运算（密钥循环使用）
        for (int i = 0; i < data.length; i++) {
            result[i] = (byte) (data[i] ^ key[i % key.length]);
        }
        return result;
    }
    // 生成ECC密钥对
    private static KeyPair generateKeyPair() throws Exception {
        KeyPairGenerator generator = KeyPairGenerator.getInstance("EC", "BC");
        generator.initialize(new ECGenParameterSpec("secp256r1"));
        return generator.generateKeyPair();
    }

    // 公钥编码为Base64（X.509格式）
    private static String encodePublicKey(PublicKey publicKey) {
        return Base64.getEncoder().encodeToString(publicKey.getEncoded());
    }

    // 私钥编码为Base64（PKCS#8格式）
    private static String encodePrivateKey(PrivateKey privateKey) {
        return Base64.getEncoder().encodeToString(privateKey.getEncoded());
    }

    // 解析公钥（关键：使用X509EncodedKeySpec）
    private static PublicKey decodePublicKey(String publicKeyBase64) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(publicKeyBase64);
        // 公钥必须用X509EncodedKeySpec解析
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory factory = KeyFactory.getInstance("EC", "BC");
        return factory.generatePublic(keySpec);
    }

    // 解析私钥（关键：使用PKCS8EncodedKeySpec）
    private static PrivateKey decodePrivateKey(String privateKeyBase64) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(privateKeyBase64);
        // 私钥必须用PKCS8EncodedKeySpec解析
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory factory = KeyFactory.getInstance("EC", "BC");
        return factory.generatePrivate(keySpec);
    }

    @Override
    public void deleteGroup(Long id) { impdataMapper.deleteById(id); }

    @Override
    public void batchDelete(List<Long> ids) {
        impdataMapper.deleteBatchIds(ids);
    }

    @Override
    public void updateVersion(int type, String version) {
        UpdateWrapper<DataUploadDO> updateWrapper = new UpdateWrapper<>();
        if (type == 1) {
            updateWrapper.set("hide_version", version);
        } else {
            updateWrapper.set("less_version", version);
        }
        dataUploadMapper.update(null, updateWrapper);
    }
    @Override
    public DataUploadDO getVersion() {
        return dataUploadMapper.selectById(1);
    }
}