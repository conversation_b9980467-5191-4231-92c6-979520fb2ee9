package cn.aguyao.module.wsapp.controller.admin.impdata;

import cn.aguyao.framework.common.pojo.CommonResult;

import cn.aguyao.module.wsapp.dal.dataobject.impdata.DataUploadDO;
import cn.aguyao.module.wsapp.service.impdata.ImpdataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.annotation.security.PermitAll;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 账号管理")
@RestController
@RequestMapping("/wsapp/imp")
@Validated
public class GetdataController {
    @Resource
    ImpdataService impdataService;

    @PostMapping("/getData")
    @PermitAll
    @Operation(summary = "获取加密数据")
    public CommonResult<String> getData(@RequestParam String str) {
        return success(impdataService.getData(str));
    }

    @GetMapping("/getVersion")
    @PermitAll
    @Operation(summary = "获取版本号")
    public CommonResult<DataUploadDO> impdata() {
        return success(impdataService.getVersion());
    }
}