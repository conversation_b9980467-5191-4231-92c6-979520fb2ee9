package cn.aguyao.module.charging.enums.wsapp;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface ApiRequestUri {

    /**
     * 系统部分， 注册 与 登录
     */
    @Getter
    @AllArgsConstructor
    enum ReqUri4SystemEnum implements BaseUriEnum {
        SEMS_CODE("/v1/login/smscode", "获取短信"),
        SEMS_LOGIN("/v1/login/smslogin", "短信登录"),
        LOGIN_URI_AZ("/v1/login/fulllogin", "六段登录安卓"),
        LOGOUT_URI_AZ("/v1/login/logout?uuid=", "退出登录"),
        ;


        private final String uri;
        private final String desc;
    }

    /**
     * 个人部分， 初始化会话
     */
    @Getter
    @AllArgsConstructor
    enum ReqUri4PrivateEnum implements BaseUriEnum{
        INIT_SESSION("/v1/message/initsession?uuid=", "初始化会话Session"),
        GET_QRCODE_URI("/v1/profile/getqrcode?uuid=", "获取个人二维码"),
        GET_LOGINQRCODE_URI("/v1/login/getqrcode", "获取登录二维码"),
        SET_NAME_URI("/v1/profile/setname?uuid=", "设置昵称"),
        SEND_TEXT("/v1/message/sendtext?uuid=", "发送文本消息"),
        SEND_VCARD("/v1/message/sendvcard?uuid=", "发送名片消息"),
        SEND_IMAGE("/v1/message/sendimage?uuid=", "发送图片消息"),
        SEND_VOICE("/v1/message/sendvoice?uuid=", "发送语音消息"),
        SEND_VIDEO("/v1/message/sendvideo?uuid=", "发送视频消息"),
        SEND_CALL("/v1/message/sendcall?uuid=", "语音视频消息"),
        SEND_IMGTEXT("/v1/message/sendcall?uuid=", "发送图文消息"),
        SEND_BUTTON("/v1/message/sendcall?uuid=", "发送超链消息"),

        ;


        private final String uri;
        private final String desc;
    }

    /**
     * 群组部分， 发送消息
     */
    @Getter
    @AllArgsConstructor
    enum ReqUri4GroupEnum implements BaseUriEnum {
        LIST_URI("/v1/group/getjoinlists?uuid=", "获取群组列表"),
        SEND_TEXT_URI("/v1/message/sendtext?uuid=", "群组发送文本消息"),
        SEND_IMAGE_URI("/v1/message/sendimage?uuid=", "群组发送图片消息"),
        SEND_VOICE_URI("/v1/message/sendimage?uuid=", "群组发送语音消息"),
        SEND_VIDEO_URI("/v1/message/sendimage?uuid=", "群组发送视频消息"),
        ;


        private final String uri;
        private final String desc;
    }

}
