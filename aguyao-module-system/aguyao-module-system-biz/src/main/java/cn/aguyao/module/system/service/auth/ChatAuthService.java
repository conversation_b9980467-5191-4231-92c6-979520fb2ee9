package cn.aguyao.module.system.service.auth;

import cn.aguyao.module.system.controller.chat.auth.vo.ChatAuthLoginReqVO;
import cn.aguyao.module.system.controller.chat.auth.vo.ChatAuthLoginRespVO;
import cn.aguyao.module.system.dal.dataobject.custservice.CustServiceDO;

import javax.validation.Valid;

/**
 * 管理后台的认证 Service 接口
 *
 * 提供用户的登录、登出的能力
 *
 * <AUTHOR>
public interface ChatAuthService {

    /**
     * 验证账号 + 密码。如果通过，则返回用户
     *
     * @param username 账号
     * @param password 密码
     * @return 用户
     */
    CustServiceDO authenticate(String username, String password);

    /**
     * 账号登录
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    ChatAuthLoginRespVO login(@Valid ChatAuthLoginReqVO reqVO);

    boolean validateToken(String token);

    /**
     * 基于 token 退出登录
     *
     * @param token token
     * @param logType 登出类型
     */
    void logout(String token, Integer logType);


    /**
     * 刷新访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 登录结果
     */
    ChatAuthLoginRespVO refreshToken(String refreshToken);

}
