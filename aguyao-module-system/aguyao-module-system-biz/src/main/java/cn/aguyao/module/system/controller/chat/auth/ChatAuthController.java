package cn.aguyao.module.system.controller.chat.auth;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.security.config.SecurityProperties;
import cn.aguyao.framework.security.core.util.SecurityFrameworkUtils;
import cn.aguyao.module.system.controller.chat.auth.vo.ChatAuthLoginReqVO;
import cn.aguyao.module.system.controller.chat.auth.vo.ChatAuthLoginRespVO;
import cn.aguyao.module.system.enums.logger.LoginLogTypeEnum;
import cn.aguyao.module.system.service.auth.ChatAuthService;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.log4j.Log4j2;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Log4j2
@Tag(name = " - 认证")
@RestController
@RequestMapping("/system/auth")
@Validated
public class ChatAuthController {

    @Resource
    private ChatAuthService authService;

    @Resource
    private SecurityProperties securityProperties;


    @PostMapping("/login")
    @PermitAll
    @Operation(summary = "使用账号密码登录")
    public CommonResult<ChatAuthLoginRespVO> login(@RequestBody ChatAuthLoginReqVO reqVO) {
        return success(authService.login(reqVO));
    }

    @PostMapping("/logout")
    @PermitAll
    @Operation(summary = "登出系统")
    public CommonResult<Boolean> logout(HttpServletRequest request) {
        String token = SecurityFrameworkUtils.obtainAuthorization(request,
                securityProperties.getTokenHeader(), securityProperties.getTokenParameter());
        if (StrUtil.isNotBlank(token)) {
            authService.logout(token, LoginLogTypeEnum.LOGOUT_SELF.getType());
        }
        return success(true);
    }

    @GetMapping("/validate-token")
    @PermitAll
    @Operation(summary = "校验Token是否有效")
    public CommonResult<Boolean> validateToken(HttpServletRequest request) {
        String token = SecurityFrameworkUtils.obtainAuthorization(request,
                securityProperties.getTokenHeader(), securityProperties.getTokenParameter());
        if (StrUtil.isBlank(token)) {
            return success(false);
        }
        boolean valid = authService.validateToken(token);
        return success(valid);
    }

    @PostMapping("/refresh-token")
    @PermitAll
    @Operation(summary = "刷新Token")
    public CommonResult<ChatAuthLoginRespVO> refreshToken(@RequestParam("refreshToken") String refreshToken) {
        if (StrUtil.isBlank(refreshToken)) {
            return CommonResult.error(401, "刷新Token不能为空");
        }
        ChatAuthLoginRespVO respVO = authService.refreshToken(refreshToken);
        return success(respVO);
    }

}
