package cn.aguyao.module.system.dal.mysql.custservice;

import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.module.system.dal.dataobject.custservice.CustServiceDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 客服账号信息表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CustServiceMapper extends BaseMapperX<CustServiceDO> {

    default CustServiceDO getUserByUsername(String username) {
        return selectOne(CustServiceDO::getAccount, username);
    }
}