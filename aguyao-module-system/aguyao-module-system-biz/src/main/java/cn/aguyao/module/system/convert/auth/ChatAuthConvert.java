package cn.aguyao.module.system.convert.auth;

import cn.aguyao.module.system.controller.chat.auth.vo.ChatAuthLoginRespVO;
import cn.aguyao.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ChatAuthConvert {

    ChatAuthConvert INSTANCE = Mappers.getMapper(ChatAuthConvert.class);

    ChatAuthLoginRespVO convert(OAuth2AccessTokenDO bean);
}
