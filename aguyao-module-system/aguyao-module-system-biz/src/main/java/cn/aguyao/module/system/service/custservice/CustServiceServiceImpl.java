package cn.aguyao.module.system.service.custservice;

import cn.aguyao.module.system.dal.dataobject.custservice.CustServiceDO;
import cn.aguyao.module.system.dal.mysql.custservice.CustServiceMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.system.enums.ErrorCodeConstants.CUST_SERVICE_ACCOUNT_NOT_EXISTS;

/**
 * 客服账号信息表 Service 实现类
 *
 * <AUTHOR>
 */
@Log4j2
@Service
@Validated
public class CustServiceServiceImpl implements CustServiceService {

    @Resource
    private CustServiceMapper custServiceMapper;

    @Resource
    private PasswordEncoder passwordEncoder;


    private void validateCustServiceExists(Long id) {
        if (custServiceMapper.selectById(id) == null) {
            throw exception(CUST_SERVICE_ACCOUNT_NOT_EXISTS);
        }
    }

    @Override
    public CustServiceDO getCustService(Long id) {
        return custServiceMapper.selectById(id);
    }

    @Override
    public CustServiceDO getUserByUsername(String username) {
        return custServiceMapper.getUserByUsername(username);
    }

    @Override
    public boolean isPasswordMatch(String rawPassword, String encodedPassword) {
        String s = passwordEncoder.encode(rawPassword);
        log.info("encode pass ==================: {}", s);
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

}