package cn.aguyao.module.system.controller.chat.tenant;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.system.controller.chat.tenant.vo.TenantSimpleRespVO;
import cn.aguyao.module.system.dal.dataobject.tenant.TenantDO;
import cn.aguyao.module.system.service.tenant.TenantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "租户")
@RestController
@RequestMapping("/system/tenant")
public class ChatTenantController {

    @Resource
    private TenantService tenantService;

    @GetMapping("/get-by-website")
    @PermitAll
    @Operation(summary = "使用域名，获得租户信息", description = "登录界面，根据用户的域名，获得租户信息")
    @Parameter(name = "website", description = "域名", required = true, example = "www.iocoder.cn")
    public CommonResult<TenantSimpleRespVO> getTenantByWebsite(@RequestParam("website") String website) {
        TenantDO tenant = tenantService.getTenantByWebsite(website);
        return success(BeanUtils.toBean(tenant, TenantSimpleRespVO.class));
    }
}
