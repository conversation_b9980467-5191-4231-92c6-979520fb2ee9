package cn.aguyao.module.system.service.auth;

import cn.aguyao.framework.common.enums.UserTypeEnum;
import cn.aguyao.framework.common.util.date.DateUtils;
import cn.aguyao.framework.common.util.monitor.TracerUtils;
import cn.aguyao.framework.common.util.servlet.ServletUtils;
import cn.aguyao.module.system.api.logger.dto.LoginLogCreateReqDTO;
import cn.aguyao.module.system.controller.chat.auth.vo.ChatAuthLoginReqVO;
import cn.aguyao.module.system.controller.chat.auth.vo.ChatAuthLoginRespVO;
import cn.aguyao.module.system.convert.auth.ChatAuthConvert;
import cn.aguyao.module.system.dal.dataobject.custservice.CustServiceDO;
import cn.aguyao.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import cn.aguyao.module.system.enums.logger.LoginLogTypeEnum;
import cn.aguyao.module.system.enums.logger.LoginResultEnum;
import cn.aguyao.module.system.enums.oauth2.OAuth2ClientConstants;
import cn.aguyao.module.system.service.custservice.CustServiceService;
import cn.aguyao.module.system.service.logger.LoginLogService;
import cn.aguyao.module.system.service.oauth2.OAuth2TokenService;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Objects;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.aguyao.module.system.enums.ErrorCodeConstants.*;

/**
 * Auth Service 实现类
 * <AUTHOR>
 */
@Service
@Log4j2
public class ChatAuthServiceImpl implements ChatAuthService {

    @Resource
    private CustServiceService userService;
    @Resource
    private LoginLogService loginLogService;
    @Resource
    private OAuth2TokenService oauth2TokenService;


    @Override
    public CustServiceDO authenticate(String username, String password) {
        final LoginLogTypeEnum logTypeEnum = LoginLogTypeEnum.LOGIN_USERNAME;
        // 校验账号是否存在
        CustServiceDO user = userService.getUserByUsername(username);
        if (user == null) {
            createLoginLog(null, username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }

        if (!userService.isPasswordMatch(password, user.getPassword())) {
            createLoginLog(user.getId(), username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        return user;
    }

    @Override
    public ChatAuthLoginRespVO login(ChatAuthLoginReqVO reqVO) {
        // 使用账号密码，进行登录
        CustServiceDO user = authenticate(reqVO.getUsername(), reqVO.getPassword());
        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user.getId(), reqVO.getUsername(), LoginLogTypeEnum.LOGIN_USERNAME);
    }


    private void createLoginLog(Long userId, String username,
                                LoginLogTypeEnum logTypeEnum, LoginResultEnum loginResult) {
        // 插入登录日志
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(logTypeEnum.getType());
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(getUserType().getValue());
        reqDTO.setUsername(username);
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(ServletUtils.getClientIP());
        reqDTO.setResult(loginResult.getResult());
        loginLogService.createLoginLog(reqDTO);
    }

    private ChatAuthLoginRespVO createTokenAfterLoginSuccess(Long userId, String username, LoginLogTypeEnum logType) {
        // 插入登陆日志
        createLoginLog(userId, username, logType, LoginResultEnum.SUCCESS);
        // 创建访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.createAccessToken(userId, getUserType().getValue(),
                OAuth2ClientConstants.CLIENT_ID_CHAT, null);
        // 构建返回结果
        return ChatAuthConvert.INSTANCE.convert(accessTokenDO);
    }

    @Override
    public ChatAuthLoginRespVO refreshToken(String refreshToken) {
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.refreshAccessToken(refreshToken, OAuth2ClientConstants.CLIENT_ID_CHAT);
        return ChatAuthConvert.INSTANCE.convert(accessTokenDO);
    }

    @Override
    public boolean validateToken(String token) {
        OAuth2AccessTokenDO oAuth2AccessTokenDO = oauth2TokenService.checkAccessToken(token);
        if (Objects.isNull(oAuth2AccessTokenDO)) {
            return false;
        }

        if (DateUtils.isExpired(oAuth2AccessTokenDO.getExpiresTime())) {
            return false;
        }

        return true;
    }

    @Override
    public void logout(String token, Integer logType) {
        // 删除访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.removeAccessToken(token);
        if (accessTokenDO == null) {
            return;
        }
        // 删除成功，则记录登出日志
        createLogoutLog(accessTokenDO.getUserId(), accessTokenDO.getUserType(), logType);
    }

    private void createLogoutLog(Long userId, Integer userType, Integer logType) {
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(logType);
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(userType);
        if (ObjectUtil.equal(getUserType().getValue(), userType)) {
            reqDTO.setUsername(getUsername(userId));
        }
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(ServletUtils.getClientIP());
        reqDTO.setResult(LoginResultEnum.SUCCESS.getResult());
        loginLogService.createLoginLog(reqDTO);
    }

    private String getUsername(Long userId) {
        if (userId == null) {
            return null;
        }
        CustServiceDO user = userService.getCustService(userId);
        return user != null ? user.getAccount() : null;
    }

    private UserTypeEnum getUserType() {
        return UserTypeEnum.CUSTSERVICE;
    }

}
