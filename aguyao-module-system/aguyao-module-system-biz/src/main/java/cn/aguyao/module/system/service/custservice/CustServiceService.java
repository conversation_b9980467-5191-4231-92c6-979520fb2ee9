package cn.aguyao.module.system.service.custservice;

import cn.aguyao.module.system.dal.dataobject.custservice.CustServiceDO;

/**
 * 客服账号信息表 Service 接口
 *
 * <AUTHOR>
 */
public interface CustServiceService {


    /**
     * 获得客服账号信息表
     *
     * @param id 编号
     * @return 客服账号信息表
     */
    CustServiceDO getCustService(Long id);

    /**
     * 获得客服账号信息表
     * @param username 用户名
     * @return 客服账号信息表
     */
    CustServiceDO getUserByUsername(String username);
    /**
     * 判断密码是否匹配
     *
     * @param rawPassword 未加密的密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    boolean isPasswordMatch(String rawPassword, String encodedPassword);
}